"""
Audio Processing Module for Meeting Notes Application

This module provides comprehensive audio file processing capabilities including:
- Audio/video file loading and analysis
- Format conversion and optimization
- Metadata extraction and analysis
- Audio preprocessing for transcription

Main Components:
- AudioProcessor: Core audio file processing
- AudioFormatConverter: Format conversion utilities
- MetadataAnalyzer: Comprehensive metadata extraction
"""

from .audio_processor import AudioProcessor, AudioFileInfo, audio_processor
from .format_converter import AudioFormatConverter, AudioFormat, ConversionQuality, format_converter
from .metadata_analyzer import MetadataAnalyzer, AudioMetadata, metadata_analyzer

__all__ = [
    'AudioProcessor',
    'AudioFileInfo',
    'audio_processor',
    'AudioFormatConverter',
    'AudioFormat',
    'ConversionQuality',
    'format_converter',
    'MetadataAnalyzer',
    'AudioMetadata',
    'metadata_analyzer'
]

# Version info
__version__ = "1.0.0"
__author__ = "Meeting Notes Application"
