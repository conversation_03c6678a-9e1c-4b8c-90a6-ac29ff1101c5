"""
Transcription Engine for Meeting Notes Application
High-performance audio transcription using faster-whisper
"""

import os
import time
import threading
from pathlib import Path
from typing import Dict, List, Optional, Callable, Generator, Any
from dataclasses import dataclass
from enum import Enum

try:
    from faster_whisper import WhisperModel
    FASTER_WHISPER_AVAILABLE = True
except ImportError:
    FASTER_WHISPER_AVAILABLE = False

from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger("transcription_engine")


class ModelSize(Enum):
    """Available Whisper model sizes"""
    TINY = "tiny"
    BASE = "base"
    SMALL = "small"
    MEDIUM = "medium"
    LARGE = "large-v2"
    LARGE_V3 = "large-v3"


class DeviceType(Enum):
    """Available compute devices"""
    CPU = "cpu"
    CUDA = "cuda"
    AUTO = "auto"


@dataclass
class TranscriptionSegment:
    """Individual transcription segment"""
    start: float
    end: float
    text: str
    confidence: float
    speaker_id: Optional[int] = None
    language: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'start': self.start,
            'end': self.end,
            'text': self.text,
            'confidence': self.confidence,
            'speaker_id': self.speaker_id,
            'language': self.language
        }


@dataclass
class TranscriptionResult:
    """Complete transcription result"""
    segments: List[TranscriptionSegment]
    full_text: str
    language: str
    duration: float
    processing_time: float
    model_used: str
    confidence_score: float
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'segments': [seg.to_dict() for seg in self.segments],
            'full_text': self.full_text,
            'language': self.language,
            'duration': self.duration,
            'processing_time': self.processing_time,
            'model_used': self.model_used,
            'confidence_score': self.confidence_score
        }


class TranscriptionEngine:
    """High-performance transcription engine using faster-whisper"""
    
    def __init__(self):
        self.model = None
        self.current_model_size = None
        self.current_device = None
        self.is_model_loaded = False
        
        # Default settings
        self.default_settings = {
            'model_size': ModelSize.BASE,
            'device': DeviceType.AUTO,
            'language': None,  # Auto-detect
            'beam_size': 5,
            'best_of': 5,
            'temperature': 0.0,
            'compression_ratio_threshold': 2.4,
            'log_prob_threshold': -1.0,
            'no_speech_threshold': 0.6,
            'condition_on_previous_text': True,
            'initial_prompt': None,
            'word_timestamps': True,
            'vad_filter': True,
            'vad_parameters': {
                'threshold': 0.5,
                'min_speech_duration_ms': 250,
                'max_speech_duration_s': 30,
                'min_silence_duration_ms': 2000,
                'speech_pad_ms': 400
            }
        }
        
    def load_model(self, model_size: ModelSize = ModelSize.BASE, 
                   device: DeviceType = DeviceType.AUTO,
                   progress_callback: Optional[Callable] = None) -> bool:
        """Load Whisper model"""
        if not FASTER_WHISPER_AVAILABLE:
            raise RuntimeError("faster-whisper is not available. Please install it with: pip install faster-whisper")
            
        try:
            if progress_callback:
                progress_callback(10, f"Loading {model_size.value} model...")
                
            logger.info(f"Loading Whisper model: {model_size.value} on {device.value}")
            
            # Determine device
            if device == DeviceType.AUTO:
                try:
                    import torch
                    actual_device = "cuda" if torch.cuda.is_available() else "cpu"
                except ImportError:
                    actual_device = "cpu"
            else:
                actual_device = device.value
                
            if progress_callback:
                progress_callback(30, f"Initializing model on {actual_device}...")
                
            # Load model
            self.model = WhisperModel(
                model_size.value,
                device=actual_device,
                compute_type="float16" if actual_device == "cuda" else "int8"
            )
            
            self.current_model_size = model_size
            self.current_device = actual_device
            self.is_model_loaded = True
            
            if progress_callback:
                progress_callback(100, f"Model {model_size.value} loaded successfully")
                
            logger.info(f"Model loaded successfully: {model_size.value} on {actual_device}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")
            self.is_model_loaded = False
            raise
            
    def transcribe_file(self, audio_path: str, 
                       settings: Optional[Dict] = None,
                       progress_callback: Optional[Callable] = None,
                       segment_callback: Optional[Callable] = None) -> TranscriptionResult:
        """Transcribe audio file with real-time progress"""
        if not self.is_model_loaded:
            raise RuntimeError("Model not loaded. Call load_model() first.")
            
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
            
        # Merge settings with defaults
        transcription_settings = self.default_settings.copy()
        if settings:
            transcription_settings.update(settings)
            
        logger.info(f"Starting transcription: {audio_path}")
        start_time = time.time()
        
        try:
            if progress_callback:
                progress_callback(5, "Preparing audio for transcription...")
                
            # Get audio duration for progress calculation
            try:
                from ..audio.audio_processor import audio_processor
                file_info = audio_processor.analyze_file(audio_path)
                total_duration = file_info.duration
            except Exception:
                total_duration = 0
                
            if progress_callback:
                progress_callback(10, "Starting transcription...")
                
            # Perform transcription
            segments, info = self.model.transcribe(
                audio_path,
                language=transcription_settings['language'],
                beam_size=transcription_settings['beam_size'],
                best_of=transcription_settings['best_of'],
                temperature=transcription_settings['temperature'],
                compression_ratio_threshold=transcription_settings['compression_ratio_threshold'],
                log_prob_threshold=transcription_settings['log_prob_threshold'],
                no_speech_threshold=transcription_settings['no_speech_threshold'],
                condition_on_previous_text=transcription_settings['condition_on_previous_text'],
                initial_prompt=transcription_settings['initial_prompt'],
                word_timestamps=transcription_settings['word_timestamps'],
                vad_filter=transcription_settings['vad_filter'],
                vad_parameters=transcription_settings['vad_parameters']
            )
            
            # Process segments with real-time callbacks
            transcription_segments = []
            full_text_parts = []
            total_confidence = 0.0
            segment_count = 0
            
            for segment in segments:
                # Create transcription segment
                trans_segment = TranscriptionSegment(
                    start=segment.start,
                    end=segment.end,
                    text=segment.text.strip(),
                    confidence=segment.avg_logprob,
                    language=info.language
                )
                
                transcription_segments.append(trans_segment)
                full_text_parts.append(trans_segment.text)
                total_confidence += trans_segment.confidence
                segment_count += 1
                
                # Real-time segment callback
                if segment_callback:
                    segment_callback(trans_segment)
                    
                # Progress update based on time
                if progress_callback and total_duration > 0:
                    progress = min(95, 10 + int((segment.end / total_duration) * 80))
                    progress_callback(progress, f"Transcribing... {self._format_time(segment.end)}")
                    
            if progress_callback:
                progress_callback(98, "Finalizing transcription...")
                
            # Calculate final metrics
            processing_time = time.time() - start_time
            average_confidence = total_confidence / segment_count if segment_count > 0 else 0.0
            full_text = " ".join(full_text_parts)
            
            # Create result
            result = TranscriptionResult(
                segments=transcription_segments,
                full_text=full_text,
                language=info.language,
                duration=info.duration,
                processing_time=processing_time,
                model_used=self.current_model_size.value,
                confidence_score=average_confidence
            )
            
            if progress_callback:
                progress_callback(100, f"Transcription complete! ({len(transcription_segments)} segments)")
                
            logger.info(f"Transcription completed: {len(transcription_segments)} segments, "
                       f"{processing_time:.2f}s processing time")
            
            return result
            
        except Exception as e:
            logger.error(f"Error during transcription: {str(e)}")
            raise
            
    def transcribe_streaming(self, audio_path: str,
                           settings: Optional[Dict] = None,
                           progress_callback: Optional[Callable] = None) -> Generator[TranscriptionSegment, None, None]:
        """Stream transcription results in real-time"""
        if not self.is_model_loaded:
            raise RuntimeError("Model not loaded. Call load_model() first.")
            
        transcription_settings = self.default_settings.copy()
        if settings:
            transcription_settings.update(settings)
            
        logger.info(f"Starting streaming transcription: {audio_path}")
        
        try:
            if progress_callback:
                progress_callback(10, "Starting streaming transcription...")
                
            segments, info = self.model.transcribe(
                audio_path,
                language=transcription_settings['language'],
                beam_size=transcription_settings['beam_size'],
                best_of=transcription_settings['best_of'],
                temperature=transcription_settings['temperature'],
                word_timestamps=transcription_settings['word_timestamps'],
                vad_filter=transcription_settings['vad_filter'],
                vad_parameters=transcription_settings['vad_parameters']
            )
            
            for segment in segments:
                trans_segment = TranscriptionSegment(
                    start=segment.start,
                    end=segment.end,
                    text=segment.text.strip(),
                    confidence=segment.avg_logprob,
                    language=info.language
                )
                
                if progress_callback:
                    progress_callback(50, f"Streaming... {self._format_time(segment.end)}")
                    
                yield trans_segment
                
        except Exception as e:
            logger.error(f"Error during streaming transcription: {str(e)}")
            raise
            
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model"""
        return {
            'is_loaded': self.is_model_loaded,
            'model_size': self.current_model_size.value if self.current_model_size else None,
            'device': self.current_device,
            'available': FASTER_WHISPER_AVAILABLE
        }
        
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages"""
        # Whisper supported languages
        return [
            'auto', 'en', 'zh', 'de', 'es', 'ru', 'ko', 'fr', 'ja', 'pt', 'tr', 'pl', 'ca', 'nl',
            'ar', 'sv', 'it', 'id', 'hi', 'fi', 'vi', 'he', 'uk', 'el', 'ms', 'cs', 'ro', 'da',
            'hu', 'ta', 'no', 'th', 'ur', 'hr', 'bg', 'lt', 'la', 'mi', 'ml', 'cy', 'sk', 'te',
            'fa', 'lv', 'bn', 'sr', 'az', 'sl', 'kn', 'et', 'mk', 'br', 'eu', 'is', 'hy', 'ne',
            'mn', 'bs', 'kk', 'sq', 'sw', 'gl', 'mr', 'pa', 'si', 'km', 'sn', 'yo', 'so', 'af',
            'oc', 'ka', 'be', 'tg', 'sd', 'gu', 'am', 'yi', 'lo', 'uz', 'fo', 'ht', 'ps', 'tk',
            'nn', 'mt', 'sa', 'lb', 'my', 'bo', 'tl', 'mg', 'as', 'tt', 'haw', 'ln', 'ha', 'ba',
            'jw', 'su'
        ]
        
    def unload_model(self):
        """Unload the current model to free memory"""
        if self.model:
            del self.model
            self.model = None
            self.is_model_loaded = False
            self.current_model_size = None
            self.current_device = None
            logger.info("Model unloaded successfully")
            
    def _format_time(self, seconds: float) -> str:
        """Format time in MM:SS format"""
        minutes = int(seconds // 60)
        secs = int(seconds % 60)
        return f"{minutes:02d}:{secs:02d}"


# Global transcription engine instance
transcription_engine = TranscriptionEngine()
