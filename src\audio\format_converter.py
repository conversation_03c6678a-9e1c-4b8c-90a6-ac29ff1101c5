"""
Audio Format Converter for Meeting Notes Application
Handles conversion between different audio formats for optimal transcription
"""

import os
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Callable
from enum import Enum

try:
    import pydub
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False

try:
    import moviepy.editor as mp
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False

from ..utils.logger import get_logger

logger = get_logger("format_converter")


class AudioFormat(Enum):
    """Supported audio formats"""
    WAV = "wav"
    MP3 = "mp3"
    M4A = "m4a"
    AAC = "aac"
    OGG = "ogg"
    FLAC = "flac"


class ConversionQuality(Enum):
    """Audio conversion quality presets"""
    LOW = {"bitrate": "64k", "sample_rate": 16000}
    MEDIUM = {"bitrate": "128k", "sample_rate": 22050}
    HIGH = {"bitrate": "192k", "sample_rate": 44100}
    TRANSCRIPTION = {"bitrate": "128k", "sample_rate": 16000}  # Optimized for speech recognition


class AudioFormatConverter:
    """Handles audio format conversion with quality optimization"""
    
    def __init__(self):
        self.temp_dir = Path(tempfile.gettempdir()) / "meeting_notes_conversion"
        self.temp_dir.mkdir(exist_ok=True)
        
        # Format compatibility matrix
        self.format_support = {
            AudioFormat.WAV: {"pydub": True, "moviepy": True},
            AudioFormat.MP3: {"pydub": True, "moviepy": True},
            AudioFormat.M4A: {"pydub": True, "moviepy": True},
            AudioFormat.AAC: {"pydub": True, "moviepy": False},
            AudioFormat.OGG: {"pydub": True, "moviepy": False},
            AudioFormat.FLAC: {"pydub": True, "moviepy": False}
        }
        
    def get_supported_formats(self) -> List[AudioFormat]:
        """Get list of supported output formats"""
        supported = []
        for fmt, support in self.format_support.items():
            if (PYDUB_AVAILABLE and support["pydub"]) or (MOVIEPY_AVAILABLE and support["moviepy"]):
                supported.append(fmt)
        return supported
        
    def is_conversion_needed(self, input_path: str, target_format: AudioFormat) -> bool:
        """Check if conversion is needed"""
        input_ext = Path(input_path).suffix.lower().lstrip('.')
        return input_ext != target_format.value
        
    def convert_for_transcription(self, input_path: str, progress_callback: Optional[Callable] = None) -> str:
        """Convert audio to optimal format for transcription (16kHz WAV)"""
        logger.info(f"Converting audio for transcription: {input_path}")
        
        output_path = self._generate_output_path(input_path, AudioFormat.WAV, "transcription")
        
        try:
            if progress_callback:
                progress_callback(10, "Preparing audio for transcription...")
                
            # Use transcription-optimized settings
            quality = ConversionQuality.TRANSCRIPTION
            
            if PYDUB_AVAILABLE:
                result = self._convert_with_pydub(
                    input_path, output_path, AudioFormat.WAV, quality, progress_callback
                )
            elif MOVIEPY_AVAILABLE:
                result = self._convert_with_moviepy(
                    input_path, output_path, AudioFormat.WAV, quality, progress_callback
                )
            else:
                raise RuntimeError("No audio conversion libraries available")
                
            if progress_callback:
                progress_callback(100, "Conversion complete")
                
            logger.info(f"Audio converted for transcription: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Error converting audio for transcription: {str(e)}")
            raise
            
    def convert_audio(self, input_path: str, target_format: AudioFormat, 
                     quality: ConversionQuality = ConversionQuality.HIGH,
                     progress_callback: Optional[Callable] = None) -> str:
        """Convert audio to specified format and quality"""
        logger.info(f"Converting {input_path} to {target_format.value}")
        
        if not self.is_conversion_needed(input_path, target_format):
            logger.info("No conversion needed, returning original file")
            return input_path
            
        output_path = self._generate_output_path(input_path, target_format)
        
        try:
            if progress_callback:
                progress_callback(10, f"Converting to {target_format.value.upper()}...")
                
            # Choose conversion method based on format support
            if PYDUB_AVAILABLE and self.format_support[target_format]["pydub"]:
                result = self._convert_with_pydub(
                    input_path, output_path, target_format, quality, progress_callback
                )
            elif MOVIEPY_AVAILABLE and self.format_support[target_format]["moviepy"]:
                result = self._convert_with_moviepy(
                    input_path, output_path, target_format, quality, progress_callback
                )
            else:
                raise ValueError(f"Format {target_format.value} not supported with available libraries")
                
            if progress_callback:
                progress_callback(100, "Conversion complete")
                
            logger.info(f"Audio converted successfully: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Error converting audio: {str(e)}")
            raise
            
    def _convert_with_pydub(self, input_path: str, output_path: str, 
                           target_format: AudioFormat, quality: ConversionQuality,
                           progress_callback: Optional[Callable] = None) -> str:
        """Convert audio using PyDub"""
        if progress_callback:
            progress_callback(30, "Loading audio with PyDub...")
            
        # Load audio file
        audio = AudioSegment.from_file(input_path)
        
        if progress_callback:
            progress_callback(50, "Applying quality settings...")
            
        # Apply quality settings
        quality_settings = quality.value
        
        # Resample if needed
        if audio.frame_rate != quality_settings["sample_rate"]:
            audio = audio.set_frame_rate(quality_settings["sample_rate"])
            
        # Convert to mono for transcription if specified
        if quality == ConversionQuality.TRANSCRIPTION and audio.channels > 1:
            audio = audio.set_channels(1)
            
        if progress_callback:
            progress_callback(80, "Exporting converted audio...")
            
        # Export with format-specific parameters
        export_params = {"format": target_format.value}
        
        if target_format in [AudioFormat.MP3, AudioFormat.AAC, AudioFormat.OGG]:
            export_params["bitrate"] = quality_settings["bitrate"]
        elif target_format == AudioFormat.WAV:
            # WAV doesn't use bitrate, use sample width instead
            export_params["parameters"] = ["-acodec", "pcm_s16le"]
            
        audio.export(output_path, **export_params)
        
        return output_path
        
    def _convert_with_moviepy(self, input_path: str, output_path: str,
                             target_format: AudioFormat, quality: ConversionQuality,
                             progress_callback: Optional[Callable] = None) -> str:
        """Convert audio using MoviePy"""
        if progress_callback:
            progress_callback(30, "Loading audio with MoviePy...")
            
        # Load audio file
        audio = mp.AudioFileClip(input_path)
        
        if progress_callback:
            progress_callback(50, "Applying quality settings...")
            
        # Apply quality settings
        quality_settings = quality.value
        
        # Set audio properties
        if hasattr(audio, 'fps') and audio.fps != quality_settings["sample_rate"]:
            audio = audio.set_fps(quality_settings["sample_rate"])
            
        if progress_callback:
            progress_callback(80, "Exporting converted audio...")
            
        # Export audio
        audio.write_audiofile(
            output_path,
            fps=quality_settings["sample_rate"],
            verbose=False,
            logger=None
        )
        
        audio.close()
        return output_path
        
    def _generate_output_path(self, input_path: str, target_format: AudioFormat, 
                             suffix: str = "converted") -> str:
        """Generate output path for converted file"""
        input_file = Path(input_path)
        output_filename = f"{input_file.stem}_{suffix}.{target_format.value}"
        return str(self.temp_dir / output_filename)
        
    def batch_convert(self, input_paths: List[str], target_format: AudioFormat,
                     quality: ConversionQuality = ConversionQuality.HIGH,
                     progress_callback: Optional[Callable] = None) -> List[str]:
        """Convert multiple audio files"""
        logger.info(f"Batch converting {len(input_paths)} files to {target_format.value}")
        
        converted_paths = []
        total_files = len(input_paths)
        
        for i, input_path in enumerate(input_paths):
            try:
                if progress_callback:
                    overall_progress = int((i / total_files) * 100)
                    progress_callback(overall_progress, f"Converting file {i+1}/{total_files}")
                    
                converted_path = self.convert_audio(input_path, target_format, quality)
                converted_paths.append(converted_path)
                
            except Exception as e:
                logger.error(f"Error converting {input_path}: {str(e)}")
                # Continue with other files
                continue
                
        if progress_callback:
            progress_callback(100, f"Batch conversion complete: {len(converted_paths)}/{total_files} files")
            
        logger.info(f"Batch conversion completed: {len(converted_paths)} successful conversions")
        return converted_paths
        
    def optimize_for_transcription(self, input_path: str, 
                                  progress_callback: Optional[Callable] = None) -> str:
        """Optimize audio specifically for speech transcription"""
        logger.info(f"Optimizing audio for transcription: {input_path}")
        
        if not PYDUB_AVAILABLE:
            logger.warning("PyDub not available, using basic conversion")
            return self.convert_for_transcription(input_path, progress_callback)
            
        try:
            if progress_callback:
                progress_callback(10, "Loading audio for optimization...")
                
            audio = AudioSegment.from_file(input_path)
            
            if progress_callback:
                progress_callback(30, "Applying speech optimization...")
                
            # Optimize for speech recognition
            # 1. Convert to mono (speech recognition works better with mono)
            if audio.channels > 1:
                audio = audio.set_channels(1)
                
            # 2. Set optimal sample rate for speech (16kHz)
            if audio.frame_rate != 16000:
                audio = audio.set_frame_rate(16000)
                
            # 3. Normalize volume
            target_dBFS = -20
            change_in_dBFS = target_dBFS - audio.dBFS
            audio = audio.apply_gain(change_in_dBFS)
            
            # 4. Apply high-pass filter to remove low-frequency noise
            audio = audio.high_pass_filter(80)
            
            # 5. Apply low-pass filter to remove high-frequency noise
            audio = audio.low_pass_filter(8000)  # Nyquist frequency for 16kHz
            
            if progress_callback:
                progress_callback(80, "Exporting optimized audio...")
                
            # Export optimized audio
            output_path = self._generate_output_path(input_path, AudioFormat.WAV, "optimized")
            audio.export(output_path, format="wav")
            
            if progress_callback:
                progress_callback(100, "Optimization complete")
                
            logger.info(f"Audio optimized for transcription: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Error optimizing audio: {str(e)}")
            # Fallback to basic conversion
            return self.convert_for_transcription(input_path, progress_callback)
            
    def get_conversion_info(self, input_path: str, target_format: AudioFormat) -> Dict[str, any]:
        """Get information about the conversion that would be performed"""
        input_file = Path(input_path)
        
        info = {
            "input_file": input_file.name,
            "input_format": input_file.suffix.lower().lstrip('.'),
            "target_format": target_format.value,
            "conversion_needed": self.is_conversion_needed(input_path, target_format),
            "supported": target_format in self.get_supported_formats(),
            "estimated_size_change": self._estimate_size_change(input_path, target_format)
        }
        
        return info
        
    def _estimate_size_change(self, input_path: str, target_format: AudioFormat) -> str:
        """Estimate how file size will change after conversion"""
        input_size = os.path.getsize(input_path)
        input_ext = Path(input_path).suffix.lower().lstrip('.')
        
        # Rough size estimates based on format characteristics
        size_factors = {
            "wav": 1.0,      # Uncompressed baseline
            "flac": 0.6,     # Lossless compression
            "mp3": 0.1,      # Lossy compression
            "m4a": 0.1,      # Lossy compression
            "aac": 0.1,      # Lossy compression
            "ogg": 0.12      # Lossy compression
        }
        
        input_factor = size_factors.get(input_ext, 0.5)
        target_factor = size_factors.get(target_format.value, 0.5)
        
        estimated_ratio = target_factor / input_factor
        
        if estimated_ratio > 1.2:
            return "Larger (uncompressed format)"
        elif estimated_ratio < 0.8:
            return "Smaller (compressed format)"
        else:
            return "Similar size"
            
    def cleanup_converted_files(self):
        """Clean up temporary converted files"""
        try:
            if self.temp_dir.exists():
                for temp_file in self.temp_dir.glob("*"):
                    if temp_file.is_file():
                        temp_file.unlink()
                logger.info("Cleaned up converted audio files")
        except Exception as e:
            logger.warning(f"Error cleaning up converted files: {str(e)}")


# Global format converter instance
format_converter = AudioFormatConverter()
