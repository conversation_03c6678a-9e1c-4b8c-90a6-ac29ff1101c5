"""
Audio Tab for Meeting Notes Application
Handles audio/video file loading and preview
"""

import os
from pathlib import Path
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
    QLabel, QPushButton, QFileDialog, QListWidget,
    QListWidgetItem, QProgressBar, QTextEdit,
    QSplitter, QFrame, QGridLayout, QSpacerItem,
    QSizePolicy, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt6.QtGui import QFont, QPixmap, QIcon


class AudioFileProcessor(QThread):
    """Thread for processing audio files"""
    progress_updated = pyqtSignal(int, str)
    file_processed = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)

    def __init__(self, file_path):
        super().__init__()
        self.file_path = file_path

    def run(self):
        """Process audio file and extract metadata"""
        try:
            # Import the audio processor
            from ..audio.audio_processor import audio_processor

            # Analyze the file using the audio processor
            file_info = audio_processor.analyze_file(
                self.file_path,
                progress_callback=self.progress_updated.emit
            )

            # Convert to dictionary for signal emission
            file_data = file_info.to_dict()
            self.file_processed.emit(file_data)

        except Exception as e:
            self.error_occurred.emit(f"Error processing file: {str(e)}")


class AudioConversionThread(QThread):
    """Thread for converting audio files for transcription"""
    progress_updated = pyqtSignal(int, str)
    conversion_completed = pyqtSignal(str)
    error_occurred = pyqtSignal(str)

    def __init__(self, file_path):
        super().__init__()
        self.file_path = file_path

    def run(self):
        """Convert audio file for optimal transcription"""
        try:
            # Import the format converter
            from ..audio.format_converter import format_converter

            # Convert audio for transcription
            converted_path = format_converter.convert_for_transcription(
                self.file_path,
                progress_callback=self.progress_updated.emit
            )

            self.conversion_completed.emit(converted_path)

        except Exception as e:
            self.error_occurred.emit(f"Error converting audio: {str(e)}")


class AudioTab(QWidget):
    """Audio file management tab"""
    
    file_loaded = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.current_file_info = None
        self.processor_thread = None
        
        self.init_ui()
        
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # File loading section
        file_section = self.create_file_section()
        layout.addWidget(file_section)
        
        # File information section
        info_section = self.create_info_section()
        layout.addWidget(info_section)
        
        # Recent files section
        recent_section = self.create_recent_files_section()
        layout.addWidget(recent_section)
        
        layout.addStretch()
        
    def create_file_section(self):
        """Create file loading section"""
        group = QGroupBox("Load Audio/Video File")
        group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        
        layout = QVBoxLayout(group)
        layout.setSpacing(15)
        
        # Instructions
        instructions = QLabel(
            "Select an audio or video file to begin transcription.\n"
            "Supported formats: MP3, WAV, M4A, MP4, AVI, MOV"
        )
        instructions.setWordWrap(True)
        instructions.setStyleSheet("color: #6c757d; font-size: 11px;")
        layout.addWidget(instructions)
        
        # File selection buttons
        button_layout = QHBoxLayout()
        
        self.browse_btn = QPushButton("📁 Browse Files")
        self.browse_btn.setMinimumHeight(40)
        self.browse_btn.clicked.connect(self.browse_files)
        button_layout.addWidget(self.browse_btn)

        self.convert_btn = QPushButton("🔄 Convert for Transcription")
        self.convert_btn.setMinimumHeight(40)
        self.convert_btn.setEnabled(False)
        self.convert_btn.clicked.connect(self.convert_for_transcription)
        button_layout.addWidget(self.convert_btn)
        
        self.drag_drop_label = QLabel("or drag and drop files here")
        self.drag_drop_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.drag_drop_label.setStyleSheet(
            "border: 2px dashed #dee2e6; "
            "border-radius: 5px; "
            "padding: 20px; "
            "color: #6c757d; "
            "background-color: #f8f9fa;"
        )
        self.drag_drop_label.setMinimumHeight(80)
        
        layout.addLayout(button_layout)
        layout.addWidget(self.drag_drop_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("")
        self.status_label.setStyleSheet("color: #28a745; font-weight: bold;")
        self.status_label.setVisible(False)
        layout.addWidget(self.status_label)
        
        return group
        
    def create_info_section(self):
        """Create file information section"""
        group = QGroupBox("File Information")
        group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # File info labels
        self.info_labels = {
            'name': QLabel("No file loaded"),
            'size': QLabel("-"),
            'duration': QLabel("-"),
            'type': QLabel("-"),
            'path': QLabel("-")
        }
        
        # Add labels to grid
        layout.addWidget(QLabel("File Name:"), 0, 0)
        layout.addWidget(self.info_labels['name'], 0, 1)
        
        layout.addWidget(QLabel("File Size:"), 1, 0)
        layout.addWidget(self.info_labels['size'], 1, 1)
        
        layout.addWidget(QLabel("Duration:"), 2, 0)
        layout.addWidget(self.info_labels['duration'], 2, 1)
        
        layout.addWidget(QLabel("Type:"), 3, 0)
        layout.addWidget(self.info_labels['type'], 3, 1)
        
        layout.addWidget(QLabel("Path:"), 4, 0)
        layout.addWidget(self.info_labels['path'], 4, 1)
        
        # Style info labels
        for label in self.info_labels.values():
            label.setStyleSheet("color: #495057; font-weight: normal;")
            label.setWordWrap(True)
            
        return group
        
    def create_recent_files_section(self):
        """Create recent files section"""
        group = QGroupBox("Recent Files")
        group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        
        layout = QVBoxLayout(group)
        
        self.recent_files_list = QListWidget()
        self.recent_files_list.setMaximumHeight(150)
        self.recent_files_list.itemDoubleClicked.connect(self.load_recent_file)
        
        # Add some placeholder items
        self.recent_files_list.addItem("No recent files")
        
        layout.addWidget(self.recent_files_list)
        
        return group
        
    def browse_files(self):
        """Open file browser dialog"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Audio/Video File",
            "",
            "Audio/Video Files (*.mp3 *.wav *.m4a *.mp4 *.avi *.mov);;All Files (*)"
        )
        
        if file_path:
            self.load_file(file_path)
            
    def load_file(self, file_path):
        """Load and process audio/video file"""
        if not os.path.exists(file_path):
            QMessageBox.warning(self, "Error", "File not found!")
            return
            
        # Show progress
        self.progress_bar.setVisible(True)
        self.status_label.setVisible(True)
        self.status_label.setText("Processing file...")
        self.browse_btn.setEnabled(False)
        
        # Start processing thread
        self.processor_thread = AudioFileProcessor(file_path)
        self.processor_thread.progress_updated.connect(self.update_progress)
        self.processor_thread.file_processed.connect(self.on_file_processed)
        self.processor_thread.error_occurred.connect(self.on_error)
        self.processor_thread.start()
        
    def update_progress(self, value, message):
        """Update progress bar and status"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
        
    def on_file_processed(self, file_info):
        """Handle successful file processing"""
        self.current_file_info = file_info

        # Update file information display
        self.info_labels['name'].setText(file_info['name'])
        self.info_labels['size'].setText(self.format_file_size(file_info['size']))
        self.info_labels['type'].setText(file_info['file_type'].title())
        self.info_labels['path'].setText(file_info['file_path'])

        if file_info['duration'] > 0:
            self.info_labels['duration'].setText(self.format_duration(file_info['duration']))
        else:
            self.info_labels['duration'].setText("Unknown")

        # Show additional audio info if available
        if file_info['has_audio']:
            additional_info = f"Sample Rate: {file_info['sample_rate']:,} Hz, Channels: {file_info['channels']}"
            self.status_label.setText(f"✅ File loaded successfully! {additional_info}")
        else:
            self.status_label.setText("⚠️ File loaded but no audio detected!")

        # Hide progress
        self.progress_bar.setVisible(False)
        self.browse_btn.setEnabled(True)

        # Enable convert button if file has audio
        self.convert_btn.setEnabled(file_info['has_audio'])

        # Emit signal
        self.file_loaded.emit(file_info['file_path'])

        # Add to recent files
        self.add_to_recent_files(file_info['file_path'])
        
    def on_error(self, error_message):
        """Handle processing error"""
        self.progress_bar.setVisible(False)
        self.status_label.setText(f"❌ Error: {error_message}")
        self.browse_btn.setEnabled(True)
        
        QMessageBox.critical(self, "Error", error_message)
        
    def load_recent_file(self, item):
        """Load a file from recent files list"""
        file_path = item.text()
        if os.path.exists(file_path):
            self.load_file(file_path)
        else:
            QMessageBox.warning(self, "Error", "File no longer exists!")
            
    def add_to_recent_files(self, file_path):
        """Add file to recent files list"""
        # Remove "No recent files" placeholder
        if self.recent_files_list.count() == 1 and self.recent_files_list.item(0).text() == "No recent files":
            self.recent_files_list.clear()
            
        # Check if file already exists in list
        for i in range(self.recent_files_list.count()):
            if self.recent_files_list.item(i).text() == file_path:
                self.recent_files_list.takeItem(i)
                break
                
        # Add to top of list
        self.recent_files_list.insertItem(0, file_path)
        
        # Limit to 10 recent files
        while self.recent_files_list.count() > 10:
            self.recent_files_list.takeItem(self.recent_files_list.count() - 1)
            
    def format_file_size(self, size_bytes):
        """Format file size in human readable format"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f} {size_names[i]}"
        
    def format_duration(self, duration_seconds):
        """Format duration in human readable format"""
        if duration_seconds <= 0:
            return "Unknown"
            
        hours = int(duration_seconds // 3600)
        minutes = int((duration_seconds % 3600) // 60)
        seconds = int(duration_seconds % 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
            
    def convert_for_transcription(self):
        """Convert current audio file for optimal transcription"""
        if not self.current_file_info or not self.current_file_info['has_audio']:
            QMessageBox.warning(self, "Error", "No audio file loaded!")
            return

        # Start conversion in a separate thread
        self.conversion_thread = AudioConversionThread(self.current_file_info['file_path'])
        self.conversion_thread.progress_updated.connect(self.update_progress)
        self.conversion_thread.conversion_completed.connect(self.on_conversion_completed)
        self.conversion_thread.error_occurred.connect(self.on_error)

        # Show progress
        self.progress_bar.setVisible(True)
        self.status_label.setVisible(True)
        self.status_label.setText("Converting audio for transcription...")
        self.convert_btn.setEnabled(False)
        self.browse_btn.setEnabled(False)

        self.conversion_thread.start()

    def on_conversion_completed(self, converted_path):
        """Handle successful audio conversion"""
        self.progress_bar.setVisible(False)
        self.status_label.setText(f"✅ Audio converted and ready for transcription!")
        self.convert_btn.setEnabled(True)
        self.browse_btn.setEnabled(True)

        # Update current file info with converted path
        if self.current_file_info:
            self.current_file_info['converted_path'] = converted_path

        QMessageBox.information(
            self,
            "Conversion Complete",
            f"Audio has been optimized for transcription.\nConverted file: {Path(converted_path).name}"
        )

    def get_current_file(self):
        """Get currently loaded file information"""
        return self.current_file_info
