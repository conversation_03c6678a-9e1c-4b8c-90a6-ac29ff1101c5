"""
Audio Processing Module for Meeting Notes Application
Handles audio/video file loading, conversion, and preprocessing
"""

import os
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any, Optional, Tuple, Union
import threading
import time

try:
    import moviepy.editor as mp
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False
    
try:
    import pydub
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False

try:
    import librosa
    import soundfile as sf
    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False

from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger("audio_processor")


class AudioFileInfo:
    """Container for audio file information"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.name = Path(file_path).name
        self.extension = Path(file_path).suffix.lower()
        self.size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        self.duration = 0.0
        self.sample_rate = 0
        self.channels = 0
        self.has_audio = False
        self.file_type = self._determine_file_type()
        self.temp_audio_path = None
        
    def _determine_file_type(self) -> str:
        """Determine if file is audio or video"""
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm'}
        audio_extensions = {'.mp3', '.wav', '.m4a', '.aac', '.ogg', '.flac', '.wma'}
        
        if self.extension in video_extensions:
            return 'video'
        elif self.extension in audio_extensions:
            return 'audio'
        else:
            return 'unknown'
            
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'file_path': self.file_path,
            'name': self.name,
            'extension': self.extension,
            'size': self.size,
            'duration': self.duration,
            'sample_rate': self.sample_rate,
            'channels': self.channels,
            'has_audio': self.has_audio,
            'file_type': self.file_type
        }


class AudioProcessor:
    """Main audio processing class"""
    
    def __init__(self):
        self.temp_dir = Path(tempfile.gettempdir()) / "meeting_notes_audio"
        self.temp_dir.mkdir(exist_ok=True)
        self.supported_formats = {
            'audio': ['.mp3', '.wav', '.m4a', '.aac', '.ogg', '.flac', '.wma'],
            'video': ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
        }
        
    def is_supported_format(self, file_path: str) -> bool:
        """Check if file format is supported"""
        extension = Path(file_path).suffix.lower()
        all_supported = self.supported_formats['audio'] + self.supported_formats['video']
        return extension in all_supported
        
    def analyze_file(self, file_path: str, progress_callback=None) -> AudioFileInfo:
        """Analyze audio/video file and extract metadata"""
        logger.info(f"Analyzing file: {file_path}")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
            
        if not self.is_supported_format(file_path):
            raise ValueError(f"Unsupported file format: {Path(file_path).suffix}")
            
        file_info = AudioFileInfo(file_path)
        
        try:
            if progress_callback:
                progress_callback(10, "Analyzing file properties...")
                
            # Try different methods based on available libraries
            if MOVIEPY_AVAILABLE:
                file_info = self._analyze_with_moviepy(file_info, progress_callback)
            elif PYDUB_AVAILABLE:
                file_info = self._analyze_with_pydub(file_info, progress_callback)
            elif LIBROSA_AVAILABLE:
                file_info = self._analyze_with_librosa(file_info, progress_callback)
            else:
                logger.warning("No audio processing libraries available, using basic analysis")
                file_info.has_audio = True  # Assume it has audio
                
            if progress_callback:
                progress_callback(100, "Analysis complete")
                
            logger.info(f"File analysis complete: {file_info.to_dict()}")
            return file_info
            
        except Exception as e:
            logger.error(f"Error analyzing file {file_path}: {str(e)}")
            raise
            
    def _analyze_with_moviepy(self, file_info: AudioFileInfo, progress_callback=None) -> AudioFileInfo:
        """Analyze file using MoviePy"""
        try:
            if progress_callback:
                progress_callback(30, "Loading with MoviePy...")
                
            if file_info.file_type == 'video':
                # Load as video file
                video = mp.VideoFileClip(file_info.file_path)
                
                if video.audio is not None:
                    file_info.has_audio = True
                    file_info.duration = video.audio.duration
                    file_info.sample_rate = video.audio.fps
                    file_info.channels = video.audio.nchannels if hasattr(video.audio, 'nchannels') else 2
                else:
                    file_info.has_audio = False
                    
                video.close()
                
            else:
                # Load as audio file
                audio = mp.AudioFileClip(file_info.file_path)
                file_info.has_audio = True
                file_info.duration = audio.duration
                file_info.sample_rate = audio.fps
                file_info.channels = audio.nchannels if hasattr(audio, 'nchannels') else 2
                audio.close()
                
            if progress_callback:
                progress_callback(70, "MoviePy analysis complete")
                
        except Exception as e:
            logger.warning(f"MoviePy analysis failed: {str(e)}")
            # Fallback to other methods
            if PYDUB_AVAILABLE:
                file_info = self._analyze_with_pydub(file_info, progress_callback)
            else:
                raise
                
        return file_info
        
    def _analyze_with_pydub(self, file_info: AudioFileInfo, progress_callback=None) -> AudioFileInfo:
        """Analyze file using PyDub"""
        try:
            if progress_callback:
                progress_callback(30, "Loading with PyDub...")
                
            # PyDub can handle many formats
            audio = AudioSegment.from_file(file_info.file_path)
            
            file_info.has_audio = True
            file_info.duration = len(audio) / 1000.0  # Convert ms to seconds
            file_info.sample_rate = audio.frame_rate
            file_info.channels = audio.channels
            
            if progress_callback:
                progress_callback(70, "PyDub analysis complete")
                
        except Exception as e:
            logger.warning(f"PyDub analysis failed: {str(e)}")
            # Fallback to librosa
            if LIBROSA_AVAILABLE:
                file_info = self._analyze_with_librosa(file_info, progress_callback)
            else:
                raise
                
        return file_info
        
    def _analyze_with_librosa(self, file_info: AudioFileInfo, progress_callback=None) -> AudioFileInfo:
        """Analyze file using librosa"""
        try:
            if progress_callback:
                progress_callback(30, "Loading with librosa...")
                
            # Librosa primarily handles audio files
            y, sr = librosa.load(file_info.file_path, sr=None)
            
            file_info.has_audio = True
            file_info.duration = len(y) / sr
            file_info.sample_rate = sr
            file_info.channels = 1 if y.ndim == 1 else y.shape[0]
            
            if progress_callback:
                progress_callback(70, "Librosa analysis complete")
                
        except Exception as e:
            logger.warning(f"Librosa analysis failed: {str(e)}")
            raise
            
        return file_info
        
    def extract_audio(self, file_info: AudioFileInfo, output_format='wav', progress_callback=None) -> str:
        """Extract audio from video file or convert audio format"""
        logger.info(f"Extracting audio from: {file_info.file_path}")
        
        if not file_info.has_audio:
            raise ValueError("File does not contain audio")
            
        # Generate output path
        output_filename = f"{Path(file_info.name).stem}_extracted.{output_format}"
        output_path = self.temp_dir / output_filename
        
        try:
            if progress_callback:
                progress_callback(10, "Starting audio extraction...")
                
            if MOVIEPY_AVAILABLE:
                output_path = self._extract_with_moviepy(file_info, output_path, progress_callback)
            elif PYDUB_AVAILABLE:
                output_path = self._extract_with_pydub(file_info, output_path, output_format, progress_callback)
            else:
                raise RuntimeError("No audio processing libraries available for extraction")
                
            if progress_callback:
                progress_callback(100, "Audio extraction complete")
                
            logger.info(f"Audio extracted to: {output_path}")
            file_info.temp_audio_path = str(output_path)
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error extracting audio: {str(e)}")
            raise
            
    def _extract_with_moviepy(self, file_info: AudioFileInfo, output_path: Path, progress_callback=None) -> Path:
        """Extract audio using MoviePy"""
        if file_info.file_type == 'video':
            # Extract audio from video
            video = mp.VideoFileClip(file_info.file_path)
            if video.audio is None:
                video.close()
                raise ValueError("Video file does not contain audio")
                
            if progress_callback:
                progress_callback(50, "Extracting audio track...")
                
            video.audio.write_audiofile(str(output_path), verbose=False, logger=None)
            video.close()
        else:
            # Convert audio file
            audio = mp.AudioFileClip(file_info.file_path)
            if progress_callback:
                progress_callback(50, "Converting audio format...")
                
            audio.write_audiofile(str(output_path), verbose=False, logger=None)
            audio.close()
            
        return output_path
        
    def _extract_with_pydub(self, file_info: AudioFileInfo, output_path: Path, output_format: str, progress_callback=None) -> Path:
        """Extract/convert audio using PyDub"""
        if progress_callback:
            progress_callback(30, "Loading audio with PyDub...")
            
        audio = AudioSegment.from_file(file_info.file_path)
        
        if progress_callback:
            progress_callback(70, "Exporting audio...")
            
        # Export in specified format
        audio.export(str(output_path), format=output_format)
        
        return output_path
        
    def preprocess_audio(self, audio_path: str, normalize=True, noise_reduction=False, progress_callback=None) -> str:
        """Preprocess audio for better transcription quality"""
        logger.info(f"Preprocessing audio: {audio_path}")
        
        if not PYDUB_AVAILABLE:
            logger.warning("PyDub not available, skipping preprocessing")
            return audio_path
            
        try:
            if progress_callback:
                progress_callback(10, "Loading audio for preprocessing...")
                
            audio = AudioSegment.from_file(audio_path)
            
            # Apply preprocessing steps
            if normalize:
                if progress_callback:
                    progress_callback(40, "Normalizing audio volume...")
                audio = self._normalize_audio(audio)
                
            if noise_reduction:
                if progress_callback:
                    progress_callback(70, "Applying noise reduction...")
                audio = self._reduce_noise(audio)
                
            # Save preprocessed audio
            preprocessed_path = Path(audio_path).parent / f"preprocessed_{Path(audio_path).name}"
            
            if progress_callback:
                progress_callback(90, "Saving preprocessed audio...")
                
            audio.export(str(preprocessed_path), format="wav")
            
            if progress_callback:
                progress_callback(100, "Preprocessing complete")
                
            logger.info(f"Audio preprocessed and saved to: {preprocessed_path}")
            return str(preprocessed_path)
            
        except Exception as e:
            logger.error(f"Error preprocessing audio: {str(e)}")
            return audio_path  # Return original if preprocessing fails
            
    def _normalize_audio(self, audio: AudioSegment) -> AudioSegment:
        """Normalize audio volume"""
        # Normalize to -20 dBFS (good level for speech)
        target_dBFS = -20
        change_in_dBFS = target_dBFS - audio.dBFS
        return audio.apply_gain(change_in_dBFS)
        
    def _reduce_noise(self, audio: AudioSegment) -> AudioSegment:
        """Basic noise reduction using high-pass filter"""
        # Apply high-pass filter to remove low-frequency noise
        # This is a simple approach; more sophisticated noise reduction would require additional libraries
        return audio.high_pass_filter(80)  # Remove frequencies below 80Hz
        
    def cleanup_temp_files(self, file_info: AudioFileInfo = None):
        """Clean up temporary audio files"""
        try:
            if file_info and file_info.temp_audio_path:
                temp_path = Path(file_info.temp_audio_path)
                if temp_path.exists():
                    temp_path.unlink()
                    logger.info(f"Cleaned up temp file: {temp_path}")
                    
                # Also clean up preprocessed files
                preprocessed_path = temp_path.parent / f"preprocessed_{temp_path.name}"
                if preprocessed_path.exists():
                    preprocessed_path.unlink()
                    logger.info(f"Cleaned up preprocessed file: {preprocessed_path}")
            else:
                # Clean up all temp files
                if self.temp_dir.exists():
                    for temp_file in self.temp_dir.glob("*"):
                        if temp_file.is_file():
                            temp_file.unlink()
                    logger.info("Cleaned up all temporary audio files")
                    
        except Exception as e:
            logger.warning(f"Error cleaning up temp files: {str(e)}")
            
    def get_audio_info_summary(self, file_info: AudioFileInfo) -> str:
        """Get a human-readable summary of audio information"""
        if not file_info.has_audio:
            return "No audio content detected"
            
        duration_str = self._format_duration(file_info.duration)
        size_str = self._format_file_size(file_info.size)
        
        summary = f"""
Audio Information:
• Duration: {duration_str}
• Sample Rate: {file_info.sample_rate:,} Hz
• Channels: {file_info.channels} ({'Stereo' if file_info.channels == 2 else 'Mono' if file_info.channels == 1 else 'Multi-channel'})
• File Size: {size_str}
• Format: {file_info.extension.upper()}
• Type: {file_info.file_type.title()}
        """.strip()
        
        return summary
        
    def _format_duration(self, seconds: float) -> str:
        """Format duration in human-readable format"""
        if seconds <= 0:
            return "Unknown"
            
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        else:
            return f"{minutes:02d}:{secs:02d}"
            
    def _format_file_size(self, size_bytes: int) -> str:
        """Format file size in human-readable format"""
        if size_bytes == 0:
            return "0 B"
            
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        size = float(size_bytes)
        
        while size >= 1024 and i < len(size_names) - 1:
            size /= 1024.0
            i += 1
            
        return f"{size:.1f} {size_names[i]}"


# Global audio processor instance
audio_processor = AudioProcessor()
