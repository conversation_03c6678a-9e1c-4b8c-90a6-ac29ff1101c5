"""
Settings Tab for Meeting Notes Application
Configuration interface for all application settings
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
    QLabel, QPushButton, QLineEdit, QComboBox,
    QCheckBox, QSpinBox, QSlider, QTabWidget,
    QFileDialog, QMessageBox, QTextEdit, QScrollArea,
    QGridLayout, QFrame, QButtonGroup, QRadioButton
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont


class SettingsTab(QWidget):
    """Application settings and configuration tab"""
    
    settings_changed = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.settings = self.load_default_settings()
        
        self.init_ui()
        
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Create settings tabs
        self.settings_tabs = QTabWidget()
        
        # AI Providers tab
        ai_tab = self.create_ai_providers_tab()
        self.settings_tabs.addTab(ai_tab, "🤖 AI Providers")
        
        # Transcription tab
        transcription_tab = self.create_transcription_tab()
        self.settings_tabs.addTab(transcription_tab, "🎤 Transcription")
        
        # Export tab
        export_tab = self.create_export_tab()
        self.settings_tabs.addTab(export_tab, "📤 Export")
        
        # General tab
        general_tab = self.create_general_tab()
        self.settings_tabs.addTab(general_tab, "⚙️ General")
        
        layout.addWidget(self.settings_tabs)
        
        # Action buttons
        button_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("💾 Save Settings")
        self.save_btn.setMinimumHeight(40)
        self.save_btn.clicked.connect(self.save_settings)
        button_layout.addWidget(self.save_btn)
        
        self.reset_btn = QPushButton("🔄 Reset to Defaults")
        self.reset_btn.setMinimumHeight(40)
        self.reset_btn.clicked.connect(self.reset_settings)
        button_layout.addWidget(self.reset_btn)
        
        button_layout.addStretch()
        
        self.test_btn = QPushButton("🧪 Test Connection")
        self.test_btn.setMinimumHeight(40)
        self.test_btn.clicked.connect(self.test_connection)
        button_layout.addWidget(self.test_btn)
        
        layout.addLayout(button_layout)
        
    def create_ai_providers_tab(self):
        """Create AI providers configuration tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        
        # OpenAI Settings
        openai_group = QGroupBox("OpenAI Configuration")
        openai_group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        openai_layout = QGridLayout(openai_group)
        
        openai_layout.addWidget(QLabel("API Key:"), 0, 0)
        self.openai_key = QLineEdit()
        self.openai_key.setEchoMode(QLineEdit.EchoMode.Password)
        self.openai_key.setPlaceholderText("sk-...")
        openai_layout.addWidget(self.openai_key, 0, 1, 1, 2)
        
        show_openai_btn = QPushButton("👁️")
        show_openai_btn.setMaximumWidth(30)
        show_openai_btn.clicked.connect(lambda: self.toggle_password_visibility(self.openai_key))
        openai_layout.addWidget(show_openai_btn, 0, 3)
        
        openai_layout.addWidget(QLabel("Default Model:"), 1, 0)
        self.openai_model = QComboBox()
        self.openai_model.addItems(["gpt-4", "gpt-3.5-turbo", "gpt-4-turbo"])
        openai_layout.addWidget(self.openai_model, 1, 1)
        
        openai_layout.addWidget(QLabel("Organization ID:"), 1, 2)
        self.openai_org = QLineEdit()
        self.openai_org.setPlaceholderText("org-...")
        openai_layout.addWidget(self.openai_org, 1, 3)
        
        layout.addWidget(openai_group)
        
        # Google Gemini Settings
        gemini_group = QGroupBox("Google Gemini Configuration")
        gemini_group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        gemini_layout = QGridLayout(gemini_group)
        
        gemini_layout.addWidget(QLabel("API Key:"), 0, 0)
        self.gemini_key = QLineEdit()
        self.gemini_key.setEchoMode(QLineEdit.EchoMode.Password)
        self.gemini_key.setPlaceholderText("AI...")
        gemini_layout.addWidget(self.gemini_key, 0, 1, 1, 2)
        
        show_gemini_btn = QPushButton("👁️")
        show_gemini_btn.setMaximumWidth(30)
        show_gemini_btn.clicked.connect(lambda: self.toggle_password_visibility(self.gemini_key))
        gemini_layout.addWidget(show_gemini_btn, 0, 3)
        
        gemini_layout.addWidget(QLabel("Default Model:"), 1, 0)
        self.gemini_model = QComboBox()
        self.gemini_model.addItems(["gemini-pro", "gemini-pro-vision"])
        gemini_layout.addWidget(self.gemini_model, 1, 1)
        
        layout.addWidget(gemini_group)
        
        # Groq Settings
        groq_group = QGroupBox("Groq Configuration")
        groq_group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        groq_layout = QGridLayout(groq_group)
        
        groq_layout.addWidget(QLabel("API Key:"), 0, 0)
        self.groq_key = QLineEdit()
        self.groq_key.setEchoMode(QLineEdit.EchoMode.Password)
        self.groq_key.setPlaceholderText("gsk_...")
        groq_layout.addWidget(self.groq_key, 0, 1, 1, 2)
        
        show_groq_btn = QPushButton("👁️")
        show_groq_btn.setMaximumWidth(30)
        show_groq_btn.clicked.connect(lambda: self.toggle_password_visibility(self.groq_key))
        groq_layout.addWidget(show_groq_btn, 0, 3)
        
        groq_layout.addWidget(QLabel("Default Model:"), 1, 0)
        self.groq_model = QComboBox()
        self.groq_model.addItems(["mixtral-8x7b-32768", "llama2-70b-4096"])
        groq_layout.addWidget(self.groq_model, 1, 1)
        
        layout.addWidget(groq_group)
        
        # Local LLM Settings
        local_group = QGroupBox("Local LLM Configuration")
        local_group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        local_layout = QGridLayout(local_group)
        
        local_layout.addWidget(QLabel("Provider:"), 0, 0)
        self.local_provider = QComboBox()
        self.local_provider.addItems(["Ollama", "LMStudio", "Custom"])
        local_layout.addWidget(self.local_provider, 0, 1)
        
        local_layout.addWidget(QLabel("Endpoint URL:"), 0, 2)
        self.local_endpoint = QLineEdit()
        self.local_endpoint.setPlaceholderText("http://localhost:11434")
        local_layout.addWidget(self.local_endpoint, 0, 3)
        
        local_layout.addWidget(QLabel("Model Name:"), 1, 0)
        self.local_model = QLineEdit()
        self.local_model.setPlaceholderText("llama2, mistral, etc.")
        local_layout.addWidget(self.local_model, 1, 1)
        
        layout.addWidget(local_group)
        
        layout.addStretch()
        return widget
        
    def create_transcription_tab(self):
        """Create transcription settings tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        
        # Whisper Settings
        whisper_group = QGroupBox("Whisper Model Settings")
        whisper_group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        whisper_layout = QGridLayout(whisper_group)
        
        whisper_layout.addWidget(QLabel("Model Size:"), 0, 0)
        self.whisper_model = QComboBox()
        self.whisper_model.addItems(["tiny", "base", "small", "medium", "large"])
        self.whisper_model.setCurrentText("base")
        whisper_layout.addWidget(self.whisper_model, 0, 1)
        
        whisper_layout.addWidget(QLabel("Device:"), 0, 2)
        self.whisper_device = QComboBox()
        self.whisper_device.addItems(["cpu", "cuda"])
        whisper_layout.addWidget(self.whisper_device, 0, 3)
        
        whisper_layout.addWidget(QLabel("Language:"), 1, 0)
        self.whisper_language = QComboBox()
        self.whisper_language.addItems(["auto", "en", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh"])
        whisper_layout.addWidget(self.whisper_language, 1, 1)
        
        whisper_layout.addWidget(QLabel("Beam Size:"), 1, 2)
        self.whisper_beam_size = QSpinBox()
        self.whisper_beam_size.setRange(1, 10)
        self.whisper_beam_size.setValue(5)
        whisper_layout.addWidget(self.whisper_beam_size, 1, 3)
        
        layout.addWidget(whisper_group)
        
        # Speaker Diarization Settings
        diarization_group = QGroupBox("Speaker Diarization Settings")
        diarization_group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        diarization_layout = QGridLayout(diarization_group)
        
        self.enable_diarization = QCheckBox("Enable Speaker Diarization")
        self.enable_diarization.setChecked(True)
        diarization_layout.addWidget(self.enable_diarization, 0, 0, 1, 2)
        
        diarization_layout.addWidget(QLabel("Min Speakers:"), 1, 0)
        self.min_speakers = QSpinBox()
        self.min_speakers.setRange(1, 10)
        self.min_speakers.setValue(2)
        diarization_layout.addWidget(self.min_speakers, 1, 1)
        
        diarization_layout.addWidget(QLabel("Max Speakers:"), 1, 2)
        self.max_speakers = QSpinBox()
        self.max_speakers.setRange(1, 20)
        self.max_speakers.setValue(5)
        diarization_layout.addWidget(self.max_speakers, 1, 3)
        
        layout.addWidget(diarization_group)
        
        # Audio Processing Settings
        audio_group = QGroupBox("Audio Processing Settings")
        audio_group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        audio_layout = QGridLayout(audio_group)
        
        self.normalize_audio = QCheckBox("Normalize Audio Volume")
        self.normalize_audio.setChecked(True)
        audio_layout.addWidget(self.normalize_audio, 0, 0)
        
        self.noise_reduction = QCheckBox("Apply Noise Reduction")
        audio_layout.addWidget(self.noise_reduction, 0, 1)
        
        audio_layout.addWidget(QLabel("Chunk Size (seconds):"), 1, 0)
        self.chunk_size = QSpinBox()
        self.chunk_size.setRange(10, 300)
        self.chunk_size.setValue(30)
        audio_layout.addWidget(self.chunk_size, 1, 1)
        
        layout.addWidget(audio_group)
        
        layout.addStretch()
        return widget
        
    def create_export_tab(self):
        """Create export settings tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        
        # Default Export Settings
        export_group = QGroupBox("Default Export Settings")
        export_group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        export_layout = QGridLayout(export_group)
        
        export_layout.addWidget(QLabel("Default Format:"), 0, 0)
        self.default_format = QComboBox()
        self.default_format.addItems(["Word Document (.docx)", "PDF (.pdf)", "Text File (.txt)"])
        export_layout.addWidget(self.default_format, 0, 1)
        
        export_layout.addWidget(QLabel("Export Directory:"), 1, 0)
        self.export_directory = QLineEdit()
        self.export_directory.setPlaceholderText("Choose export directory...")
        export_layout.addWidget(self.export_directory, 1, 1)
        
        browse_btn = QPushButton("📁 Browse")
        browse_btn.clicked.connect(self.browse_export_directory)
        export_layout.addWidget(browse_btn, 1, 2)
        
        layout.addWidget(export_group)
        
        # Word Export Settings
        word_group = QGroupBox("Word Document Settings")
        word_group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        word_layout = QGridLayout(word_group)
        
        self.include_header = QCheckBox("Include Header with Meeting Info")
        self.include_header.setChecked(True)
        word_layout.addWidget(self.include_header, 0, 0)
        
        self.include_toc = QCheckBox("Include Table of Contents")
        word_layout.addWidget(self.include_toc, 0, 1)
        
        word_layout.addWidget(QLabel("Font:"), 1, 0)
        self.word_font = QComboBox()
        self.word_font.addItems(["Arial", "Calibri", "Times New Roman", "Helvetica"])
        word_layout.addWidget(self.word_font, 1, 1)
        
        word_layout.addWidget(QLabel("Font Size:"), 1, 2)
        self.word_font_size = QSpinBox()
        self.word_font_size.setRange(8, 24)
        self.word_font_size.setValue(11)
        word_layout.addWidget(self.word_font_size, 1, 3)
        
        layout.addWidget(word_group)
        
        # PDF Export Settings
        pdf_group = QGroupBox("PDF Export Settings")
        pdf_group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        pdf_layout = QGridLayout(pdf_group)
        
        self.pdf_page_numbers = QCheckBox("Include Page Numbers")
        self.pdf_page_numbers.setChecked(True)
        pdf_layout.addWidget(self.pdf_page_numbers, 0, 0)
        
        self.pdf_bookmarks = QCheckBox("Create Bookmarks")
        pdf_layout.addWidget(self.pdf_bookmarks, 0, 1)
        
        pdf_layout.addWidget(QLabel("Page Size:"), 1, 0)
        self.pdf_page_size = QComboBox()
        self.pdf_page_size.addItems(["A4", "Letter", "Legal"])
        pdf_layout.addWidget(self.pdf_page_size, 1, 1)
        
        layout.addWidget(pdf_group)
        
        layout.addStretch()
        return widget
        
    def create_general_tab(self):
        """Create general settings tab"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(20)
        
        # Application Settings
        app_group = QGroupBox("Application Settings")
        app_group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        app_layout = QGridLayout(app_group)
        
        self.auto_save = QCheckBox("Auto-save transcriptions")
        self.auto_save.setChecked(True)
        app_layout.addWidget(self.auto_save, 0, 0)
        
        self.show_notifications = QCheckBox("Show completion notifications")
        self.show_notifications.setChecked(True)
        app_layout.addWidget(self.show_notifications, 0, 1)
        
        app_layout.addWidget(QLabel("Theme:"), 1, 0)
        self.theme = QComboBox()
        self.theme.addItems(["Light", "Dark", "System"])
        app_layout.addWidget(self.theme, 1, 1)
        
        app_layout.addWidget(QLabel("Language:"), 1, 2)
        self.app_language = QComboBox()
        self.app_language.addItems(["English", "Spanish", "French", "German"])
        app_layout.addWidget(self.app_language, 1, 3)
        
        layout.addWidget(app_group)
        
        # Performance Settings
        perf_group = QGroupBox("Performance Settings")
        perf_group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        perf_layout = QGridLayout(perf_group)
        
        perf_layout.addWidget(QLabel("Max Concurrent Processes:"), 0, 0)
        self.max_processes = QSpinBox()
        self.max_processes.setRange(1, 8)
        self.max_processes.setValue(2)
        perf_layout.addWidget(self.max_processes, 0, 1)
        
        perf_layout.addWidget(QLabel("Memory Limit (MB):"), 0, 2)
        self.memory_limit = QSpinBox()
        self.memory_limit.setRange(512, 8192)
        self.memory_limit.setValue(2048)
        perf_layout.addWidget(self.memory_limit, 0, 3)
        
        self.gpu_acceleration = QCheckBox("Enable GPU Acceleration (if available)")
        perf_layout.addWidget(self.gpu_acceleration, 1, 0, 1, 2)
        
        layout.addWidget(perf_group)
        
        # Data Settings
        data_group = QGroupBox("Data Management")
        data_group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        data_layout = QGridLayout(data_group)
        
        data_layout.addWidget(QLabel("Data Directory:"), 0, 0)
        self.data_directory = QLineEdit()
        self.data_directory.setPlaceholderText("Choose data directory...")
        data_layout.addWidget(self.data_directory, 0, 1)
        
        browse_data_btn = QPushButton("📁 Browse")
        browse_data_btn.clicked.connect(self.browse_data_directory)
        data_layout.addWidget(browse_data_btn, 0, 2)
        
        data_layout.addWidget(QLabel("Keep transcriptions for:"), 1, 0)
        self.retention_period = QComboBox()
        self.retention_period.addItems(["Forever", "1 Year", "6 Months", "3 Months", "1 Month"])
        data_layout.addWidget(self.retention_period, 1, 1)
        
        layout.addWidget(data_group)
        
        layout.addStretch()
        return widget
        
    def toggle_password_visibility(self, line_edit):
        """Toggle password visibility for API keys"""
        if line_edit.echoMode() == QLineEdit.EchoMode.Password:
            line_edit.setEchoMode(QLineEdit.EchoMode.Normal)
        else:
            line_edit.setEchoMode(QLineEdit.EchoMode.Password)
            
    def browse_export_directory(self):
        """Browse for export directory"""
        directory = QFileDialog.getExistingDirectory(self, "Select Export Directory")
        if directory:
            self.export_directory.setText(directory)
            
    def browse_data_directory(self):
        """Browse for data directory"""
        directory = QFileDialog.getExistingDirectory(self, "Select Data Directory")
        if directory:
            self.data_directory.setText(directory)
            
    def load_default_settings(self):
        """Load default settings"""
        return {
            # AI Providers
            'openai_key': '',
            'openai_model': 'gpt-3.5-turbo',
            'openai_org': '',
            'gemini_key': '',
            'gemini_model': 'gemini-pro',
            'groq_key': '',
            'groq_model': 'mixtral-8x7b-32768',
            'local_provider': 'Ollama',
            'local_endpoint': 'http://localhost:11434',
            'local_model': 'llama2',
            
            # Transcription
            'whisper_model': 'base',
            'whisper_device': 'cpu',
            'whisper_language': 'auto',
            'whisper_beam_size': 5,
            'enable_diarization': True,
            'min_speakers': 2,
            'max_speakers': 5,
            'normalize_audio': True,
            'noise_reduction': False,
            'chunk_size': 30,
            
            # Export
            'default_format': 'Word Document (.docx)',
            'export_directory': '',
            'include_header': True,
            'include_toc': False,
            'word_font': 'Arial',
            'word_font_size': 11,
            'pdf_page_numbers': True,
            'pdf_bookmarks': False,
            'pdf_page_size': 'A4',
            
            # General
            'auto_save': True,
            'show_notifications': True,
            'theme': 'Light',
            'app_language': 'English',
            'max_processes': 2,
            'memory_limit': 2048,
            'gpu_acceleration': False,
            'data_directory': '',
            'retention_period': 'Forever'
        }
        
    def save_settings(self):
        """Save current settings"""
        # Collect all settings from UI
        self.settings.update({
            'openai_key': self.openai_key.text(),
            'openai_model': self.openai_model.currentText(),
            'openai_org': self.openai_org.text(),
            'gemini_key': self.gemini_key.text(),
            'gemini_model': self.gemini_model.currentText(),
            'groq_key': self.groq_key.text(),
            'groq_model': self.groq_model.currentText(),
            'local_provider': self.local_provider.currentText(),
            'local_endpoint': self.local_endpoint.text(),
            'local_model': self.local_model.text(),
            
            'whisper_model': self.whisper_model.currentText(),
            'whisper_device': self.whisper_device.currentText(),
            'whisper_language': self.whisper_language.currentText(),
            'whisper_beam_size': self.whisper_beam_size.value(),
            'enable_diarization': self.enable_diarization.isChecked(),
            'min_speakers': self.min_speakers.value(),
            'max_speakers': self.max_speakers.value(),
            'normalize_audio': self.normalize_audio.isChecked(),
            'noise_reduction': self.noise_reduction.isChecked(),
            'chunk_size': self.chunk_size.value(),
            
            'default_format': self.default_format.currentText(),
            'export_directory': self.export_directory.text(),
            'include_header': self.include_header.isChecked(),
            'include_toc': self.include_toc.isChecked(),
            'word_font': self.word_font.currentText(),
            'word_font_size': self.word_font_size.value(),
            'pdf_page_numbers': self.pdf_page_numbers.isChecked(),
            'pdf_bookmarks': self.pdf_bookmarks.isChecked(),
            'pdf_page_size': self.pdf_page_size.currentText(),
            
            'auto_save': self.auto_save.isChecked(),
            'show_notifications': self.show_notifications.isChecked(),
            'theme': self.theme.currentText(),
            'app_language': self.app_language.currentText(),
            'max_processes': self.max_processes.value(),
            'memory_limit': self.memory_limit.value(),
            'gpu_acceleration': self.gpu_acceleration.isChecked(),
            'data_directory': self.data_directory.text(),
            'retention_period': self.retention_period.currentText()
        })
        
        # Emit settings changed signal
        self.settings_changed.emit(self.settings)
        
        QMessageBox.information(self, "Settings Saved", "Settings have been saved successfully!")
        
    def reset_settings(self):
        """Reset settings to defaults"""
        reply = QMessageBox.question(
            self, "Reset Settings", 
            "Are you sure you want to reset all settings to defaults?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.settings = self.load_default_settings()
            self.load_settings_to_ui()
            QMessageBox.information(self, "Settings Reset", "Settings have been reset to defaults!")
            
    def test_connection(self):
        """Test AI provider connection"""
        # This would test the currently selected AI provider
        QMessageBox.information(self, "Test Connection", "Connection test functionality will be implemented")
        
    def load_settings_to_ui(self):
        """Load settings to UI components"""
        # This would load saved settings into the UI
        pass
        
    def get_settings(self):
        """Get current settings"""
        return self.settings
