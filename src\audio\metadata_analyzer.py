"""
Audio Metadata Analyzer for Meeting Notes Application
Extracts detailed metadata from audio and video files
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

try:
    import mutagen
    from mutagen.id3 import ID3NoHeaderError
    MUTAGEN_AVAILABLE = True
except ImportError:
    MUTAGEN_AVAILABLE = False

try:
    import moviepy.editor as mp
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False

try:
    import librosa
    import numpy as np
    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False

from ..utils.logger import get_logger

logger = get_logger("metadata_analyzer")


class AudioMetadata:
    """Container for comprehensive audio metadata"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.file_name = Path(file_path).name
        self.file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        self.file_extension = Path(file_path).suffix.lower()
        self.creation_date = None
        self.modification_date = None
        
        # Basic audio properties
        self.duration = 0.0
        self.sample_rate = 0
        self.channels = 0
        self.bit_depth = 0
        self.bitrate = 0
        self.format_info = {}
        
        # Content metadata
        self.title = None
        self.artist = None
        self.album = None
        self.genre = None
        self.year = None
        self.track_number = None
        self.comment = None
        
        # Technical analysis
        self.audio_quality_score = 0.0
        self.noise_level = 0.0
        self.dynamic_range = 0.0
        self.peak_frequency = 0.0
        self.speech_probability = 0.0
        self.silence_percentage = 0.0
        
        # Transcription readiness
        self.transcription_ready = False
        self.optimization_suggestions = []
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'file_info': {
                'path': self.file_path,
                'name': self.file_name,
                'size': self.file_size,
                'extension': self.file_extension,
                'creation_date': self.creation_date.isoformat() if self.creation_date else None,
                'modification_date': self.modification_date.isoformat() if self.modification_date else None
            },
            'audio_properties': {
                'duration': self.duration,
                'sample_rate': self.sample_rate,
                'channels': self.channels,
                'bit_depth': self.bit_depth,
                'bitrate': self.bitrate,
                'format_info': self.format_info
            },
            'content_metadata': {
                'title': self.title,
                'artist': self.artist,
                'album': self.album,
                'genre': self.genre,
                'year': self.year,
                'track_number': self.track_number,
                'comment': self.comment
            },
            'technical_analysis': {
                'audio_quality_score': self.audio_quality_score,
                'noise_level': self.noise_level,
                'dynamic_range': self.dynamic_range,
                'peak_frequency': self.peak_frequency,
                'speech_probability': self.speech_probability,
                'silence_percentage': self.silence_percentage
            },
            'transcription_info': {
                'ready': self.transcription_ready,
                'suggestions': self.optimization_suggestions
            }
        }


class MetadataAnalyzer:
    """Comprehensive audio metadata analyzer"""
    
    def __init__(self):
        self.supported_formats = {
            'audio': ['.mp3', '.wav', '.m4a', '.aac', '.ogg', '.flac', '.wma'],
            'video': ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
        }
        
    def analyze_file(self, file_path: str, deep_analysis: bool = True) -> AudioMetadata:
        """Perform comprehensive metadata analysis"""
        logger.info(f"Analyzing metadata for: {file_path}")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
            
        metadata = AudioMetadata(file_path)
        
        try:
            # Basic file information
            self._extract_file_info(metadata)
            
            # Audio properties and content metadata
            if MUTAGEN_AVAILABLE:
                self._extract_mutagen_metadata(metadata)
            elif MOVIEPY_AVAILABLE:
                self._extract_moviepy_metadata(metadata)
                
            # Deep technical analysis
            if deep_analysis and LIBROSA_AVAILABLE:
                self._perform_audio_analysis(metadata)
                
            # Assess transcription readiness
            self._assess_transcription_readiness(metadata)
            
            logger.info(f"Metadata analysis complete for: {file_path}")
            return metadata
            
        except Exception as e:
            logger.error(f"Error analyzing metadata: {str(e)}")
            raise
            
    def _extract_file_info(self, metadata: AudioMetadata):
        """Extract basic file system information"""
        try:
            stat = os.stat(metadata.file_path)
            metadata.creation_date = datetime.fromtimestamp(stat.st_ctime)
            metadata.modification_date = datetime.fromtimestamp(stat.st_mtime)
        except Exception as e:
            logger.warning(f"Could not extract file timestamps: {str(e)}")
            
    def _extract_mutagen_metadata(self, metadata: AudioMetadata):
        """Extract metadata using Mutagen library"""
        try:
            audio_file = mutagen.File(metadata.file_path)
            
            if audio_file is None:
                logger.warning("Mutagen could not read file")
                return
                
            # Basic audio properties
            if hasattr(audio_file, 'info'):
                info = audio_file.info
                metadata.duration = getattr(info, 'length', 0.0)
                metadata.bitrate = getattr(info, 'bitrate', 0)
                metadata.sample_rate = getattr(info, 'sample_rate', 0)
                metadata.channels = getattr(info, 'channels', 0)
                
                # Format-specific information
                metadata.format_info = {
                    'codec': getattr(info, 'codec', 'unknown'),
                    'version': getattr(info, 'version', 'unknown'),
                    'mode': getattr(info, 'mode', 'unknown')
                }
                
            # Content metadata (tags)
            if audio_file.tags:
                metadata.title = self._get_tag_value(audio_file.tags, ['TIT2', 'TITLE', '\xa9nam'])
                metadata.artist = self._get_tag_value(audio_file.tags, ['TPE1', 'ARTIST', '\xa9ART'])
                metadata.album = self._get_tag_value(audio_file.tags, ['TALB', 'ALBUM', '\xa9alb'])
                metadata.genre = self._get_tag_value(audio_file.tags, ['TCON', 'GENRE', '\xa9gen'])
                metadata.year = self._get_tag_value(audio_file.tags, ['TDRC', 'DATE', '\xa9day'])
                metadata.track_number = self._get_tag_value(audio_file.tags, ['TRCK', 'TRACKNUMBER', 'trkn'])
                metadata.comment = self._get_tag_value(audio_file.tags, ['COMM', 'COMMENT', '\xa9cmt'])
                
        except Exception as e:
            logger.warning(f"Mutagen metadata extraction failed: {str(e)}")
            
    def _get_tag_value(self, tags, tag_keys: List[str]) -> Optional[str]:
        """Get tag value from multiple possible keys"""
        for key in tag_keys:
            if key in tags:
                value = tags[key]
                if isinstance(value, list) and value:
                    return str(value[0])
                elif value:
                    return str(value)
        return None
        
    def _extract_moviepy_metadata(self, metadata: AudioMetadata):
        """Extract metadata using MoviePy as fallback"""
        try:
            if metadata.file_extension in ['.mp4', '.avi', '.mov', '.mkv']:
                # Video file
                video = mp.VideoFileClip(metadata.file_path)
                if video.audio:
                    metadata.duration = video.audio.duration
                    metadata.sample_rate = video.audio.fps
                    metadata.channels = getattr(video.audio, 'nchannels', 2)
                video.close()
            else:
                # Audio file
                audio = mp.AudioFileClip(metadata.file_path)
                metadata.duration = audio.duration
                metadata.sample_rate = audio.fps
                metadata.channels = getattr(audio, 'nchannels', 2)
                audio.close()
                
        except Exception as e:
            logger.warning(f"MoviePy metadata extraction failed: {str(e)}")
            
    def _perform_audio_analysis(self, metadata: AudioMetadata):
        """Perform deep audio analysis using librosa"""
        try:
            logger.info("Performing deep audio analysis...")
            
            # Load audio for analysis (limit to first 30 seconds for performance)
            y, sr = librosa.load(metadata.file_path, duration=30, sr=None)
            
            # Audio quality metrics
            metadata.audio_quality_score = self._calculate_quality_score(y, sr)
            metadata.noise_level = self._estimate_noise_level(y)
            metadata.dynamic_range = self._calculate_dynamic_range(y)
            
            # Frequency analysis
            metadata.peak_frequency = self._find_peak_frequency(y, sr)
            
            # Speech detection
            metadata.speech_probability = self._estimate_speech_probability(y, sr)
            
            # Silence detection
            metadata.silence_percentage = self._calculate_silence_percentage(y)
            
        except Exception as e:
            logger.warning(f"Deep audio analysis failed: {str(e)}")
            
    def _calculate_quality_score(self, y, sr) -> float:
        """Calculate overall audio quality score (0-100)"""
        try:
            # Factors: dynamic range, frequency content, noise level
            dynamic_range = np.std(y) * 100
            spectral_centroid = np.mean(librosa.feature.spectral_centroid(y=y, sr=sr))
            spectral_rolloff = np.mean(librosa.feature.spectral_rolloff(y=y, sr=sr))
            
            # Normalize and combine factors
            quality_score = min(100, (dynamic_range * 2 + spectral_centroid / 100 + spectral_rolloff / 1000) / 3)
            return max(0, quality_score)
            
        except Exception:
            return 50.0  # Default moderate quality
            
    def _estimate_noise_level(self, y) -> float:
        """Estimate background noise level"""
        try:
            # Use the quietest 10% of the audio to estimate noise floor
            sorted_samples = np.sort(np.abs(y))
            noise_floor = np.mean(sorted_samples[:int(len(sorted_samples) * 0.1)])
            return float(noise_floor * 100)
        except Exception:
            return 0.0
            
    def _calculate_dynamic_range(self, y) -> float:
        """Calculate dynamic range in dB"""
        try:
            if len(y) == 0:
                return 0.0
            rms = np.sqrt(np.mean(y**2))
            peak = np.max(np.abs(y))
            if rms > 0 and peak > 0:
                return float(20 * np.log10(peak / rms))
            return 0.0
        except Exception:
            return 0.0
            
    def _find_peak_frequency(self, y, sr) -> float:
        """Find the dominant frequency"""
        try:
            fft = np.fft.fft(y)
            magnitude = np.abs(fft)
            freqs = np.fft.fftfreq(len(fft), 1/sr)
            peak_idx = np.argmax(magnitude[:len(magnitude)//2])
            return float(abs(freqs[peak_idx]))
        except Exception:
            return 0.0
            
    def _estimate_speech_probability(self, y, sr) -> float:
        """Estimate probability that audio contains speech"""
        try:
            # Speech typically has energy in 300-3400 Hz range
            stft = librosa.stft(y)
            freqs = librosa.fft_frequencies(sr=sr)
            
            # Find indices for speech frequency range
            speech_range = (freqs >= 300) & (freqs <= 3400)
            speech_energy = np.mean(np.abs(stft[speech_range, :]))
            total_energy = np.mean(np.abs(stft))
            
            if total_energy > 0:
                speech_ratio = speech_energy / total_energy
                return min(100.0, speech_ratio * 200)  # Scale to 0-100
            return 0.0
            
        except Exception:
            return 50.0  # Default moderate probability
            
    def _calculate_silence_percentage(self, y) -> float:
        """Calculate percentage of audio that is silence"""
        try:
            # Define silence threshold (adjust as needed)
            silence_threshold = 0.01
            silent_samples = np.sum(np.abs(y) < silence_threshold)
            return float(silent_samples / len(y) * 100)
        except Exception:
            return 0.0
            
    def _assess_transcription_readiness(self, metadata: AudioMetadata):
        """Assess if audio is ready for transcription and provide suggestions"""
        suggestions = []
        
        # Check sample rate
        if metadata.sample_rate > 0:
            if metadata.sample_rate < 16000:
                suggestions.append("Sample rate is low - consider upsampling to 16kHz for better transcription")
            elif metadata.sample_rate > 48000:
                suggestions.append("Sample rate is very high - can downsample to 16kHz to improve processing speed")
                
        # Check channels
        if metadata.channels > 2:
            suggestions.append("Multi-channel audio detected - convert to mono for transcription")
        elif metadata.channels == 2:
            suggestions.append("Stereo audio - consider converting to mono for transcription")
            
        # Check duration
        if metadata.duration > 3600:  # 1 hour
            suggestions.append("Long audio file - consider splitting into smaller segments")
            
        # Check quality metrics
        if metadata.noise_level > 20:
            suggestions.append("High noise level detected - consider noise reduction")
            
        if metadata.silence_percentage > 50:
            suggestions.append("High silence percentage - consider removing silent sections")
            
        if metadata.speech_probability < 30:
            suggestions.append("Low speech probability - verify this is a speech recording")
            
        # Determine readiness
        metadata.transcription_ready = (
            metadata.duration > 0 and
            metadata.sample_rate >= 8000 and
            metadata.channels <= 2 and
            metadata.speech_probability >= 20
        )
        
        if not suggestions:
            suggestions.append("Audio appears optimized for transcription")
            
        metadata.optimization_suggestions = suggestions


# Global metadata analyzer instance
metadata_analyzer = MetadataAnalyzer()
