#!/usr/bin/env python3
"""
Meeting Notes Application
Professional desktop GUI for audio transcription and AI-powered note-taking

Features:
- Audio/video file loading with automatic audio extraction
- Real-time transcription using faster-whisper
- Speaker diarization with pyannote-audio
- AI-powered summary generation with streaming
- Q&A system for transcribed content
- Multi-format export (Word, PDF, TXT)
- Support for multiple AI providers
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from PyQt6.QtWidgets import QApplication
from gui.main_window import MainWindow


def main():
    """Main application entry point"""
    # High DPI scaling is enabled by default in PyQt6
    
    # Create application
    app = QApplication(sys.argv)
    app.setApplicationName("Meeting Notes")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Meeting Notes App")
    
    # Set application style
    app.setStyle('Fusion')
    
    # Create and show main window
    window = MainWindow()
    window.show()
    
    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
