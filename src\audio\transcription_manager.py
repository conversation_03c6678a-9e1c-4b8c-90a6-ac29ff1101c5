"""
Transcription Manager for Meeting Notes Application
Orchestrates the complete transcription workflow
"""

import os
import json
import threading
from pathlib import Path
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime

from .transcription_engine import TranscriptionEngine, TranscriptionResult, TranscriptionSegment, ModelSize, DeviceType
from .audio_processor import audio_processor
from .format_converter import format_converter
from ..utils.logger import get_logger
from ..utils.config import config

logger = get_logger("transcription_manager")


class TranscriptionSession:
    """Manages a complete transcription session"""
    
    def __init__(self, session_id: str, audio_path: str):
        self.session_id = session_id
        self.audio_path = audio_path
        self.original_audio_path = audio_path
        self.converted_audio_path = None
        self.transcription_result = None
        self.created_at = datetime.now()
        self.completed_at = None
        self.status = "initialized"
        self.error_message = None
        
        # Session metadata
        self.audio_info = None
        self.transcription_settings = {}
        self.processing_stats = {
            'audio_analysis_time': 0.0,
            'conversion_time': 0.0,
            'transcription_time': 0.0,
            'total_time': 0.0
        }
        
    def to_dict(self) -> Dict[str, Any]:
        """Convert session to dictionary for serialization"""
        return {
            'session_id': self.session_id,
            'audio_path': self.audio_path,
            'original_audio_path': self.original_audio_path,
            'converted_audio_path': self.converted_audio_path,
            'created_at': self.created_at.isoformat(),
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'status': self.status,
            'error_message': self.error_message,
            'audio_info': self.audio_info.to_dict() if self.audio_info else None,
            'transcription_settings': self.transcription_settings,
            'processing_stats': self.processing_stats,
            'transcription_result': self.transcription_result.to_dict() if self.transcription_result else None
        }


class TranscriptionManager:
    """Manages the complete transcription workflow"""
    
    def __init__(self):
        self.engine = TranscriptionEngine()
        self.active_sessions = {}
        self.session_history = []
        
        # Default transcription settings
        self.default_settings = {
            'model_size': ModelSize.BASE,
            'device': DeviceType.AUTO,
            'language': None,  # Auto-detect
            'auto_convert': True,
            'preprocessing': {
                'normalize_audio': True,
                'noise_reduction': False
            },
            'output_format': {
                'include_timestamps': True,
                'include_confidence': True,
                'segment_level': True
            }
        }
        
    def start_transcription(self, audio_path: str, 
                          settings: Optional[Dict] = None,
                          progress_callback: Optional[Callable] = None,
                          segment_callback: Optional[Callable] = None) -> str:
        """Start a new transcription session"""
        session_id = self._generate_session_id()
        session = TranscriptionSession(session_id, audio_path)
        self.active_sessions[session_id] = session
        
        # Merge settings
        transcription_settings = self.default_settings.copy()
        if settings:
            transcription_settings.update(settings)
        session.transcription_settings = transcription_settings
        
        logger.info(f"Starting transcription session {session_id} for {audio_path}")
        
        # Start transcription in background thread
        thread = threading.Thread(
            target=self._transcription_workflow,
            args=(session, progress_callback, segment_callback)
        )
        thread.daemon = True
        thread.start()
        
        return session_id
        
    def _transcription_workflow(self, session: TranscriptionSession,
                              progress_callback: Optional[Callable] = None,
                              segment_callback: Optional[Callable] = None):
        """Complete transcription workflow"""
        try:
            session.status = "analyzing_audio"
            start_time = datetime.now()
            
            # Step 1: Analyze audio file
            if progress_callback:
                progress_callback(5, "Analyzing audio file...")
                
            analysis_start = datetime.now()
            session.audio_info = audio_processor.analyze_file(
                session.audio_path,
                progress_callback=lambda p, m: progress_callback(5 + p//10, m) if progress_callback else None
            )
            session.processing_stats['audio_analysis_time'] = (datetime.now() - analysis_start).total_seconds()
            
            if not session.audio_info.has_audio:
                raise ValueError("No audio content detected in file")
                
            # Step 2: Convert audio if needed
            session.status = "converting_audio"
            if session.transcription_settings['auto_convert']:
                if progress_callback:
                    progress_callback(15, "Converting audio for optimal transcription...")
                    
                conversion_start = datetime.now()
                session.converted_audio_path = format_converter.optimize_for_transcription(
                    session.audio_path,
                    progress_callback=lambda p, m: progress_callback(15 + p//5, m) if progress_callback else None
                )
                session.processing_stats['conversion_time'] = (datetime.now() - conversion_start).total_seconds()
                
                # Use converted audio for transcription
                transcription_audio_path = session.converted_audio_path
            else:
                transcription_audio_path = session.audio_path
                
            # Step 3: Load transcription model
            session.status = "loading_model"
            if not self.engine.is_model_loaded:
                if progress_callback:
                    progress_callback(35, "Loading transcription model...")
                    
                self.engine.load_model(
                    model_size=session.transcription_settings['model_size'],
                    device=session.transcription_settings['device'],
                    progress_callback=lambda p, m: progress_callback(35 + p//5, m) if progress_callback else None
                )
                
            # Step 4: Perform transcription
            session.status = "transcribing"
            if progress_callback:
                progress_callback(55, "Starting transcription...")
                
            transcription_start = datetime.now()
            
            # Create enhanced segment callback
            def enhanced_segment_callback(segment: TranscriptionSegment):
                if segment_callback:
                    segment_callback(segment)
                logger.debug(f"Transcribed segment: {segment.start:.2f}s - {segment.end:.2f}s")
                
            session.transcription_result = self.engine.transcribe_file(
                transcription_audio_path,
                settings=session.transcription_settings,
                progress_callback=lambda p, m: progress_callback(55 + p//3, m) if progress_callback else None,
                segment_callback=enhanced_segment_callback
            )
            
            session.processing_stats['transcription_time'] = (datetime.now() - transcription_start).total_seconds()
            
            # Step 5: Finalize session
            session.status = "completed"
            session.completed_at = datetime.now()
            session.processing_stats['total_time'] = (session.completed_at - start_time).total_seconds()
            
            if progress_callback:
                progress_callback(100, f"Transcription completed! {len(session.transcription_result.segments)} segments")
                
            # Move to history
            self.session_history.append(session)
            if session.session_id in self.active_sessions:
                del self.active_sessions[session.session_id]
                
            logger.info(f"Transcription session {session.session_id} completed successfully")
            
        except Exception as e:
            session.status = "error"
            session.error_message = str(e)
            session.completed_at = datetime.now()
            
            logger.error(f"Transcription session {session.session_id} failed: {str(e)}")
            
            if progress_callback:
                progress_callback(0, f"Error: {str(e)}")
                
    def get_session_status(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a transcription session"""
        # Check active sessions
        if session_id in self.active_sessions:
            return self.active_sessions[session_id].to_dict()
            
        # Check history
        for session in self.session_history:
            if session.session_id == session_id:
                return session.to_dict()
                
        return None
        
    def get_transcription_result(self, session_id: str) -> Optional[TranscriptionResult]:
        """Get transcription result for a session"""
        session_data = self.get_session_status(session_id)
        if session_data and session_data['transcription_result']:
            # Find the actual session object
            for session in self.session_history:
                if session.session_id == session_id:
                    return session.transcription_result
        return None
        
    def cancel_transcription(self, session_id: str) -> bool:
        """Cancel an active transcription session"""
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            session.status = "cancelled"
            session.completed_at = datetime.now()
            
            # Move to history
            self.session_history.append(session)
            del self.active_sessions[session_id]
            
            logger.info(f"Transcription session {session_id} cancelled")
            return True
        return False
        
    def get_active_sessions(self) -> List[Dict[str, Any]]:
        """Get list of active transcription sessions"""
        return [session.to_dict() for session in self.active_sessions.values()]
        
    def get_session_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get transcription session history"""
        return [session.to_dict() for session in self.session_history[-limit:]]
        
    def save_session(self, session_id: str, output_path: str) -> bool:
        """Save transcription session to file"""
        session_data = self.get_session_status(session_id)
        if not session_data:
            return False
            
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)
            logger.info(f"Session {session_id} saved to {output_path}")
            return True
        except Exception as e:
            logger.error(f"Error saving session: {str(e)}")
            return False
            
    def load_session(self, input_path: str) -> Optional[str]:
        """Load transcription session from file"""
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                session_data = json.load(f)
                
            # Reconstruct session object
            session = TranscriptionSession(
                session_data['session_id'],
                session_data['audio_path']
            )
            
            # Restore session data
            session.original_audio_path = session_data.get('original_audio_path')
            session.converted_audio_path = session_data.get('converted_audio_path')
            session.created_at = datetime.fromisoformat(session_data['created_at'])
            session.completed_at = datetime.fromisoformat(session_data['completed_at']) if session_data.get('completed_at') else None
            session.status = session_data['status']
            session.error_message = session_data.get('error_message')
            session.transcription_settings = session_data.get('transcription_settings', {})
            session.processing_stats = session_data.get('processing_stats', {})
            
            # Add to history
            self.session_history.append(session)
            
            logger.info(f"Session {session.session_id} loaded from {input_path}")
            return session.session_id
            
        except Exception as e:
            logger.error(f"Error loading session: {str(e)}")
            return None
            
    def cleanup_temp_files(self, session_id: str):
        """Clean up temporary files for a session"""
        session_data = self.get_session_status(session_id)
        if session_data and session_data['converted_audio_path']:
            try:
                converted_path = Path(session_data['converted_audio_path'])
                if converted_path.exists():
                    converted_path.unlink()
                    logger.info(f"Cleaned up converted audio file: {converted_path}")
            except Exception as e:
                logger.warning(f"Error cleaning up temp files: {str(e)}")
                
    def get_model_info(self) -> Dict[str, Any]:
        """Get transcription engine model information"""
        return self.engine.get_model_info()
        
    def get_supported_languages(self) -> List[str]:
        """Get supported languages"""
        return self.engine.get_supported_languages()
        
    def _generate_session_id(self) -> str:
        """Generate unique session ID"""
        import uuid
        return str(uuid.uuid4())[:8]


# Global transcription manager instance
transcription_manager = TranscriptionManager()
