#!/usr/bin/env python3
"""
Test Audio Processing Module
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_audio_imports():
    """Test that audio processing modules can be imported"""
    try:
        print("Testing audio module imports...")

        from src.audio.audio_processor import AudioProcessor, AudioFileInfo, audio_processor
        print("✅ AudioProcessor imported successfully")

        from src.audio.format_converter import AudioFormatConverter, AudioFormat, ConversionQuality, format_converter
        print("✅ AudioFormatConverter imported successfully")

        from src.audio.metadata_analyzer import MetadataAnalyzer, AudioMetadata, metadata_analyzer
        print("✅ MetadataAnalyzer imported successfully")
        
        # Test basic functionality
        processor = AudioProcessor()
        print(f"✅ AudioProcessor instance created")
        print(f"   Supported formats: {processor.supported_formats}")
        
        converter = AudioFormatConverter()
        print(f"✅ AudioFormatConverter instance created")
        print(f"   Supported formats: {[fmt.value for fmt in converter.get_supported_formats()]}")
        
        analyzer = MetadataAnalyzer()
        print(f"✅ MetadataAnalyzer instance created")
        print(f"   Supported formats: {analyzer.supported_formats}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_file_format_detection():
    """Test file format detection"""
    try:
        print("\nTesting file format detection...")
        
        from src.audio.audio_processor import audio_processor
        
        test_files = [
            "test.mp3",
            "test.wav", 
            "test.m4a",
            "test.mp4",
            "test.avi",
            "test.mov",
            "test.txt"  # Unsupported
        ]
        
        for test_file in test_files:
            is_supported = audio_processor.is_supported_format(test_file)
            status = "✅ Supported" if is_supported else "❌ Not supported"
            print(f"   {test_file}: {status}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing format detection: {e}")
        return False

def test_audio_info_creation():
    """Test AudioFileInfo creation"""
    try:
        print("\nTesting AudioFileInfo creation...")
        
        from src.audio.audio_processor import AudioFileInfo
        
        # Test with a fake file path
        test_path = "test_audio.mp3"
        file_info = AudioFileInfo(test_path)
        
        print(f"✅ AudioFileInfo created for: {file_info.name}")
        print(f"   Extension: {file_info.extension}")
        print(f"   File type: {file_info.file_type}")
        print(f"   Dictionary: {file_info.to_dict()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating AudioFileInfo: {e}")
        return False

def test_format_conversion_info():
    """Test format conversion information"""
    try:
        print("\nTesting format conversion info...")
        
        from src.audio.format_converter import format_converter, AudioFormat
        
        test_path = "test_audio.mp3"
        target_format = AudioFormat.WAV
        
        conversion_info = format_converter.get_conversion_info(test_path, target_format)
        print(f"✅ Conversion info generated:")
        for key, value in conversion_info.items():
            print(f"   {key}: {value}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error getting conversion info: {e}")
        return False

def main():
    """Run all tests"""
    print("🎵 Testing Audio Processing Module")
    print("=" * 50)
    
    tests = [
        test_audio_imports,
        test_file_format_detection,
        test_audio_info_creation,
        test_format_conversion_info
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All audio processing tests passed!")
        return True
    else:
        print("⚠️ Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
