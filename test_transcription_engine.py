#!/usr/bin/env python3
"""
Test Transcription Engine Module
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_transcription_imports():
    """Test that transcription modules can be imported"""
    try:
        print("Testing transcription module imports...")
        
        from src.audio.transcription_engine import (
            TranscriptionEngine, TranscriptionResult, TranscriptionSegment, 
            ModelSize, DeviceType, transcription_engine
        )
        print("✅ TranscriptionEngine imported successfully")
        
        from src.audio.transcription_manager import (
            TranscriptionManager, TranscriptionSession, transcription_manager
        )
        print("✅ TranscriptionManager imported successfully")
        
        # Test basic functionality
        engine = TranscriptionEngine()
        print(f"✅ TranscriptionEngine instance created")
        print(f"   Model info: {engine.get_model_info()}")
        
        manager = TranscriptionManager()
        print(f"✅ TranscriptionManager instance created")
        print(f"   Supported languages: {len(manager.get_supported_languages())} languages")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_model_sizes_and_devices():
    """Test model size and device enums"""
    try:
        print("\nTesting model sizes and devices...")
        
        from src.audio.transcription_engine import ModelSize, DeviceType
        
        print("Available model sizes:")
        for model in ModelSize:
            print(f"   - {model.name}: {model.value}")
            
        print("Available devices:")
        for device in DeviceType:
            print(f"   - {device.name}: {device.value}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing enums: {e}")
        return False

def test_transcription_session():
    """Test transcription session creation"""
    try:
        print("\nTesting transcription session...")
        
        from src.audio.transcription_manager import TranscriptionSession
        
        # Create a test session
        session = TranscriptionSession("test-123", "test_audio.wav")
        
        print(f"✅ TranscriptionSession created:")
        print(f"   Session ID: {session.session_id}")
        print(f"   Audio path: {session.audio_path}")
        print(f"   Status: {session.status}")
        print(f"   Created at: {session.created_at}")
        
        # Test serialization
        session_dict = session.to_dict()
        print(f"✅ Session serialization works")
        print(f"   Dictionary keys: {list(session_dict.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing session: {e}")
        return False

def test_transcription_result():
    """Test transcription result structures"""
    try:
        print("\nTesting transcription result structures...")
        
        from src.audio.transcription_engine import TranscriptionSegment, TranscriptionResult
        
        # Create test segment
        segment = TranscriptionSegment(
            start=0.0,
            end=5.0,
            text="This is a test transcription segment.",
            confidence=0.95,
            speaker_id=1,
            language="en"
        )
        
        print(f"✅ TranscriptionSegment created:")
        print(f"   Text: {segment.text}")
        print(f"   Duration: {segment.end - segment.start}s")
        print(f"   Confidence: {segment.confidence}")
        
        # Test segment serialization
        segment_dict = segment.to_dict()
        print(f"✅ Segment serialization works")
        
        # Create test result
        result = TranscriptionResult(
            segments=[segment],
            full_text=segment.text,
            language="en",
            duration=5.0,
            processing_time=2.5,
            model_used="base",
            confidence_score=0.95
        )
        
        print(f"✅ TranscriptionResult created:")
        print(f"   Segments: {len(result.segments)}")
        print(f"   Language: {result.language}")
        print(f"   Processing time: {result.processing_time}s")
        
        # Test result serialization
        result_dict = result.to_dict()
        print(f"✅ Result serialization works")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing result structures: {e}")
        return False

def test_faster_whisper_availability():
    """Test if faster-whisper is available"""
    try:
        print("\nTesting faster-whisper availability...")
        
        try:
            import faster_whisper
            print("✅ faster-whisper is available")
            print(f"   Version: {getattr(faster_whisper, '__version__', 'unknown')}")
            
            # Test basic model creation (without loading)
            from faster_whisper import WhisperModel
            print("✅ WhisperModel class accessible")
            
            return True
            
        except ImportError:
            print("⚠️ faster-whisper not available")
            print("   Install with: pip install faster-whisper")
            return True  # Not a failure, just not available
            
    except Exception as e:
        print(f"❌ Error testing faster-whisper: {e}")
        return False

def test_transcription_manager_workflow():
    """Test transcription manager workflow (without actual transcription)"""
    try:
        print("\nTesting transcription manager workflow...")
        
        from src.audio.transcription_manager import transcription_manager
        from src.audio.transcription_engine import ModelSize, DeviceType
        
        # Test getting model info
        model_info = transcription_manager.get_model_info()
        print(f"✅ Model info retrieved: {model_info}")
        
        # Test getting supported languages
        languages = transcription_manager.get_supported_languages()
        print(f"✅ Supported languages: {len(languages)} languages")
        print(f"   Sample languages: {languages[:10]}")
        
        # Test session management
        active_sessions = transcription_manager.get_active_sessions()
        print(f"✅ Active sessions: {len(active_sessions)}")
        
        session_history = transcription_manager.get_session_history()
        print(f"✅ Session history: {len(session_history)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing manager workflow: {e}")
        return False

def main():
    """Run all tests"""
    print("🎤 Testing Transcription Engine Module")
    print("=" * 50)
    
    tests = [
        test_transcription_imports,
        test_model_sizes_and_devices,
        test_transcription_session,
        test_transcription_result,
        test_faster_whisper_availability,
        test_transcription_manager_workflow
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All transcription engine tests passed!")
        return True
    else:
        print("⚠️ Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
