"""
Transcription Tab for Meeting Notes Application
Handles audio transcription using faster-whisper and speaker diarization
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGroupBox,
    QLabel, QPushButton, QTextEdit, QProgressBar,
    QComboBox, QCheckBox, QSpinBox, QSlider,
    QSplitter, QFrame, QGridLayout, QScrollArea,
    QMessageBox, QTabWidget
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt6.QtGui import QFont, QTextCharFormat, QColor


class TranscriptionWorker(QThread):
    """Worker thread for audio transcription"""
    progress_updated = pyqtSignal(int, str)
    transcription_chunk = pyqtSignal(dict)  # {'text': str, 'start': float, 'end': float, 'speaker': str}
    transcription_completed = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, audio_file, settings):
        super().__init__()
        self.audio_file = audio_file
        self.settings = settings
        self.is_cancelled = False
        
    def run(self):
        """Run transcription process"""
        try:
            # Import transcription manager
            from ..audio.transcription_manager import transcription_manager
            from ..audio.transcription_engine import ModelSize, DeviceType

            # Convert settings to our format
            converted_settings = {
                'model_size': ModelSize(self.settings.get('model_size', 'base')),
                'device': DeviceType(self.settings.get('device', 'auto')),
                'language': self.settings.get('language'),
                'auto_convert': True
            }

            # Start transcription session
            self.session_id = transcription_manager.start_transcription(
                self.audio_file,
                settings=converted_settings,
                progress_callback=self.progress_updated.emit,
                segment_callback=self._on_segment_received
            )

            # Monitor session until completion
            import time
            while not self.is_cancelled:
                session_status = transcription_manager.get_session_status(self.session_id)
                if not session_status:
                    break

                status = session_status['status']

                if status == 'completed':
                    result = transcription_manager.get_transcription_result(self.session_id)
                    if result:
                        self.transcription_completed.emit(result.full_text)
                    break
                elif status == 'error':
                    error_msg = session_status.get('error_message', 'Unknown error')
                    self.error_occurred.emit(error_msg)
                    break
                elif status == 'cancelled':
                    self.error_occurred.emit("Transcription was cancelled")
                    break

                time.sleep(0.5)  # Check every 500ms
            
        except Exception as e:
            self.error_occurred.emit(f"Transcription error: {str(e)}")
            
    def format_transcript(self, segments):
        """Format transcript with timestamps and speakers"""
        formatted_lines = []
        
        for segment in segments:
            timestamp = self.format_timestamp(segment['start'])
            speaker = segment['speaker']
            text = segment['text']
            
            formatted_lines.append(f"[{timestamp}] {speaker}: {text}")
            
        return "\n\n".join(formatted_lines)
        
    def format_timestamp(self, seconds):
        """Format timestamp as MM:SS"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"
        
    def cancel(self):
        """Cancel transcription"""
        self.is_cancelled = True
        if hasattr(self, 'session_id') and self.session_id:
            from ..audio.transcription_manager import transcription_manager
            transcription_manager.cancel_transcription(self.session_id)

    def _on_segment_received(self, segment):
        """Handle real-time segment updates"""
        segment_data = {
            'text': segment.text,
            'start': segment.start,
            'end': segment.end,
            'speaker': f"Speaker {hash(segment.text) % 3 + 1}"  # Simple speaker simulation
        }
        self.transcription_chunk.emit(segment_data)


class TranscriptionTab(QWidget):
    """Transcription management tab"""
    
    transcription_completed = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.current_audio_file = None
        self.transcription_worker = None
        self.transcript_segments = []
        
        self.init_ui()
        
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Settings section
        settings_section = self.create_settings_section()
        layout.addWidget(settings_section)
        
        # Control section
        control_section = self.create_control_section()
        layout.addWidget(control_section)
        
        # Results section
        results_section = self.create_results_section()
        layout.addWidget(results_section, 1)  # Give it more space
        
    def create_settings_section(self):
        """Create transcription settings section"""
        group = QGroupBox("Transcription Settings")
        group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # Model size selection
        layout.addWidget(QLabel("Model Size:"), 0, 0)
        self.model_combo = QComboBox()
        self.model_combo.addItems(["tiny", "base", "small", "medium", "large"])
        self.model_combo.setCurrentText("base")
        layout.addWidget(self.model_combo, 0, 1)
        
        # Language selection
        layout.addWidget(QLabel("Language:"), 0, 2)
        self.language_combo = QComboBox()
        self.language_combo.addItems(["auto", "en", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh"])
        self.language_combo.setCurrentText("auto")
        layout.addWidget(self.language_combo, 0, 3)
        
        # Device selection
        layout.addWidget(QLabel("Device:"), 1, 0)
        self.device_combo = QComboBox()
        self.device_combo.addItems(["cpu", "cuda"])
        layout.addWidget(self.device_combo, 1, 1)
        
        # Speaker diarization
        self.diarization_checkbox = QCheckBox("Enable Speaker Diarization")
        self.diarization_checkbox.setChecked(True)
        layout.addWidget(self.diarization_checkbox, 1, 2, 1, 2)
        
        # Advanced settings
        layout.addWidget(QLabel("Beam Size:"), 2, 0)
        self.beam_size_spin = QSpinBox()
        self.beam_size_spin.setRange(1, 10)
        self.beam_size_spin.setValue(5)
        layout.addWidget(self.beam_size_spin, 2, 1)
        
        layout.addWidget(QLabel("Temperature:"), 2, 2)
        self.temperature_spin = QSpinBox()
        self.temperature_spin.setRange(0, 100)
        self.temperature_spin.setValue(0)
        self.temperature_spin.setSuffix("%")
        layout.addWidget(self.temperature_spin, 2, 3)
        
        return group
        
    def create_control_section(self):
        """Create transcription control section"""
        group = QGroupBox("Transcription Control")
        group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        
        layout = QVBoxLayout(group)
        layout.setSpacing(10)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("🎤 Start Transcription")
        self.start_btn.setMinimumHeight(40)
        self.start_btn.clicked.connect(self.start_transcription)
        button_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("⏹️ Stop")
        self.stop_btn.setMinimumHeight(40)
        self.stop_btn.setEnabled(False)
        self.stop_btn.clicked.connect(self.stop_transcription)
        button_layout.addWidget(self.stop_btn)
        
        self.clear_btn = QPushButton("🗑️ Clear")
        self.clear_btn.setMinimumHeight(40)
        self.clear_btn.clicked.connect(self.clear_transcript)
        button_layout.addWidget(self.clear_btn)
        
        layout.addLayout(button_layout)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Status label
        self.status_label = QLabel("Ready to transcribe")
        self.status_label.setStyleSheet("color: #6c757d; font-style: italic;")
        layout.addWidget(self.status_label)
        
        return group
        
    def create_results_section(self):
        """Create transcription results section"""
        group = QGroupBox("Transcription Results")
        group.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        
        layout = QVBoxLayout(group)
        
        # Create tab widget for different views
        self.results_tabs = QTabWidget()
        
        # Real-time view
        self.realtime_text = QTextEdit()
        self.realtime_text.setPlaceholderText("Transcription will appear here in real-time...")
        self.realtime_text.setFont(QFont("Consolas", 10))
        self.results_tabs.addTab(self.realtime_text, "📝 Real-time")
        
        # Final transcript view
        self.final_text = QTextEdit()
        self.final_text.setPlaceholderText("Final formatted transcript will appear here...")
        self.final_text.setFont(QFont("Arial", 11))
        self.results_tabs.addTab(self.final_text, "📄 Final Transcript")
        
        # Speaker segments view
        self.segments_text = QTextEdit()
        self.segments_text.setPlaceholderText("Speaker-separated segments will appear here...")
        self.segments_text.setFont(QFont("Arial", 10))
        self.results_tabs.addTab(self.segments_text, "👥 Speaker Segments")
        
        layout.addWidget(self.results_tabs)
        
        # Export buttons
        export_layout = QHBoxLayout()
        
        copy_btn = QPushButton("📋 Copy to Clipboard")
        copy_btn.clicked.connect(self.copy_transcript)
        export_layout.addWidget(copy_btn)
        
        save_btn = QPushButton("💾 Save Transcript")
        save_btn.clicked.connect(self.save_transcript)
        export_layout.addWidget(save_btn)
        
        export_layout.addStretch()
        
        layout.addLayout(export_layout)
        
        return group
        
    def set_audio_file(self, file_path):
        """Set the audio file for transcription"""
        self.current_audio_file = file_path
        self.status_label.setText(f"Ready to transcribe: {file_path}")
        self.start_btn.setEnabled(True)
        
    def start_transcription(self):
        """Start the transcription process"""
        if not self.current_audio_file:
            QMessageBox.warning(self, "No Audio File", "Please load an audio file first.")
            return
            
        # Get settings
        settings = {
            'model_size': self.model_combo.currentText(),
            'language': self.language_combo.currentText() if self.language_combo.currentText() != "auto" else None,
            'device': self.device_combo.currentText(),
            'beam_size': self.beam_size_spin.value(),
            'temperature': self.temperature_spin.value() / 100.0,
            'task': 'transcribe'
        }
        
        # Clear previous results
        self.clear_transcript()
        
        # Update UI
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # Start transcription worker
        self.transcription_worker = TranscriptionWorker(self.current_audio_file, settings)
        self.transcription_worker.progress_updated.connect(self.update_progress)
        self.transcription_worker.transcription_chunk.connect(self.add_transcription_chunk)
        self.transcription_worker.transcription_completed.connect(self.on_transcription_completed)
        self.transcription_worker.error_occurred.connect(self.on_transcription_error)
        self.transcription_worker.start()
        
    def stop_transcription(self):
        """Stop the transcription process"""
        if self.transcription_worker:
            self.transcription_worker.cancel()
            self.transcription_worker.wait()
            
        self.reset_ui_state()
        self.status_label.setText("Transcription stopped")
        
    def clear_transcript(self):
        """Clear all transcription results"""
        self.realtime_text.clear()
        self.final_text.clear()
        self.segments_text.clear()
        self.transcript_segments = []
        
    def update_progress(self, value, message):
        """Update progress bar and status"""
        self.progress_bar.setValue(value)
        self.status_label.setText(message)
        
    def add_transcription_chunk(self, segment_data):
        """Add a transcription chunk in real-time"""
        self.transcript_segments.append(segment_data)
        
        # Add to real-time view
        timestamp = self.format_timestamp(segment_data['start'])
        speaker = segment_data['speaker']
        text = segment_data['text']
        
        chunk_text = f"[{timestamp}] {speaker}: {text}\n"
        self.realtime_text.append(chunk_text)
        
        # Scroll to bottom
        cursor = self.realtime_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.realtime_text.setTextCursor(cursor)
        
    def on_transcription_completed(self, final_transcript):
        """Handle completed transcription"""
        self.final_text.setPlainText(final_transcript)
        self.update_speaker_segments()
        
        self.reset_ui_state()
        self.status_label.setText("✅ Transcription completed successfully!")
        
        # Emit signal
        self.transcription_completed.emit(final_transcript)
        
    def on_transcription_error(self, error_message):
        """Handle transcription error"""
        self.reset_ui_state()
        self.status_label.setText(f"❌ Error: {error_message}")
        QMessageBox.critical(self, "Transcription Error", error_message)
        
    def update_speaker_segments(self):
        """Update speaker segments view"""
        if not self.transcript_segments:
            return
            
        # Group by speaker
        speakers = {}
        for segment in self.transcript_segments:
            speaker = segment['speaker']
            if speaker not in speakers:
                speakers[speaker] = []
            speakers[speaker].append(segment)
            
        # Format speaker segments
        segments_text = []
        for speaker, segments in speakers.items():
            segments_text.append(f"=== {speaker} ===\n")
            for segment in segments:
                timestamp = self.format_timestamp(segment['start'])
                segments_text.append(f"[{timestamp}] {segment['text']}\n")
            segments_text.append("\n")
            
        self.segments_text.setPlainText("".join(segments_text))
        
    def reset_ui_state(self):
        """Reset UI to ready state"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        
    def format_timestamp(self, seconds):
        """Format timestamp as MM:SS"""
        minutes = int(seconds // 60)
        seconds = int(seconds % 60)
        return f"{minutes:02d}:{seconds:02d}"
        
    def copy_transcript(self):
        """Copy current transcript to clipboard"""
        current_tab = self.results_tabs.currentIndex()
        if current_tab == 0:
            text = self.realtime_text.toPlainText()
        elif current_tab == 1:
            text = self.final_text.toPlainText()
        else:
            text = self.segments_text.toPlainText()
            
        if text:
            from PyQt6.QtWidgets import QApplication
            QApplication.clipboard().setText(text)
            self.status_label.setText("Transcript copied to clipboard")
        else:
            QMessageBox.information(self, "No Content", "No transcript to copy.")
            
    def save_transcript(self):
        """Save transcript to file"""
        current_tab = self.results_tabs.currentIndex()
        if current_tab == 0:
            text = self.realtime_text.toPlainText()
            default_name = "realtime_transcript.txt"
        elif current_tab == 1:
            text = self.final_text.toPlainText()
            default_name = "final_transcript.txt"
        else:
            text = self.segments_text.toPlainText()
            default_name = "speaker_segments.txt"
            
        if not text:
            QMessageBox.information(self, "No Content", "No transcript to save.")
            return
            
        from PyQt6.QtWidgets import QFileDialog
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Save Transcript",
            default_name,
            "Text Files (*.txt);;All Files (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(text)
                self.status_label.setText(f"Transcript saved to {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "Save Error", f"Error saving file: {str(e)}")
                
    def get_transcript(self):
        """Get the current transcript"""
        return self.final_text.toPlainText()
