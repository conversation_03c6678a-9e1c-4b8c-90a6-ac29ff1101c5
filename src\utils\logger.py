"""
Logging utilities for Meeting Notes Application
Provides structured logging with file and console output
"""

import logging
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional


class MeetingNotesLogger:
    """Custom logger for the Meeting Notes application"""
    
    def __init__(self, name: str = "meeting_notes", log_dir: Optional[str] = None):
        """Initialize logger
        
        Args:
            name: Logger name
            log_dir: Directory for log files. If None, uses default.
        """
        self.name = name
        
        if log_dir is None:
            # Use user's home directory for logs
            self.log_dir = Path.home() / ".meeting_notes" / "logs"
        else:
            self.log_dir = Path(log_dir)
            
        self.log_dir.mkdir(parents=True, exist_ok=True)
        
        # Create logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # Prevent duplicate handlers
        if not self.logger.handlers:
            self._setup_handlers()
            
    def _setup_handlers(self):
        """Setup logging handlers"""
        # File handler for all logs
        log_file = self.log_dir / f"{self.name}_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # Console handler for important messages
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # Error file handler
        error_file = self.log_dir / f"{self.name}_errors_{datetime.now().strftime('%Y%m%d')}.log"
        error_handler = logging.FileHandler(error_file, encoding='utf-8')
        error_handler.setLevel(logging.ERROR)
        
        # Formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(funcName)s:%(lineno)d - %(message)s'
        )
        
        simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        
        # Apply formatters
        file_handler.setFormatter(detailed_formatter)
        console_handler.setFormatter(simple_formatter)
        error_handler.setFormatter(detailed_formatter)
        
        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        self.logger.addHandler(error_handler)
        
    def debug(self, message: str, **kwargs):
        """Log debug message"""
        self.logger.debug(message, **kwargs)
        
    def info(self, message: str, **kwargs):
        """Log info message"""
        self.logger.info(message, **kwargs)
        
    def warning(self, message: str, **kwargs):
        """Log warning message"""
        self.logger.warning(message, **kwargs)
        
    def error(self, message: str, **kwargs):
        """Log error message"""
        self.logger.error(message, **kwargs)
        
    def critical(self, message: str, **kwargs):
        """Log critical message"""
        self.logger.critical(message, **kwargs)
        
    def exception(self, message: str, **kwargs):
        """Log exception with traceback"""
        self.logger.exception(message, **kwargs)
        
    def log_transcription_start(self, file_path: str, settings: dict):
        """Log transcription start"""
        self.info(f"Starting transcription for: {file_path}")
        self.debug(f"Transcription settings: {settings}")
        
    def log_transcription_progress(self, progress: int, message: str):
        """Log transcription progress"""
        self.debug(f"Transcription progress: {progress}% - {message}")
        
    def log_transcription_complete(self, file_path: str, duration: float, word_count: int):
        """Log transcription completion"""
        self.info(f"Transcription completed for: {file_path}")
        self.info(f"Duration: {duration:.2f}s, Words: {word_count}")
        
    def log_summary_generation(self, provider: str, model: str, word_count: int):
        """Log summary generation"""
        self.info(f"Generating summary using {provider}:{model} for {word_count} words")
        
    def log_export(self, format_type: str, file_path: str):
        """Log export operation"""
        self.info(f"Exported {format_type} to: {file_path}")
        
    def log_error_with_context(self, error: Exception, context: dict):
        """Log error with additional context"""
        self.error(f"Error: {str(error)}")
        self.debug(f"Error context: {context}")
        self.exception("Full traceback:")


# Global logger instance
logger = MeetingNotesLogger()


def get_logger(name: str = "meeting_notes") -> MeetingNotesLogger:
    """Get logger instance
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return MeetingNotesLogger(name)
