# -*- coding: utf-8 -*-
# Copyright 2023 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Helper module for post_process_utils_test."""
from __future__ import annotations

from google.generativeai.notebook import post_process_utils


def add_length(x: str) -> int:
    return len(x)


@post_process_utils.post_process_add_fn
def add_length_decorated(x: str) -> int:
    return len(x)


@post_process_utils.post_process_replace_fn
def to_upper(x: str) -> str:
    return x.upper()
