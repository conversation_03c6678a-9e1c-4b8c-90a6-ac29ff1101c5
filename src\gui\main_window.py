"""
Main Window for Meeting Notes Application
Professional desktop GUI for audio transcription and AI-powered note-taking
"""

import sys
from pathlib import Path
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QMenuBar, QStatusBar, QToolBar,
    QFileDialog, QMessageBox, QSplitter,
    QTextEdit, QLabel, QPushButton, QProgressBar,
    QGroupBox, QGridLayout, QComboBox, QSpinBox,
    QCheckBox, QLineEdit, QListWidget, QFrame
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QIcon, QFont, QPixmap, QAction

from .audio_tab import AudioTab
from .transcription_tab import TranscriptionTab
from .summary_tab import SummaryTab
from .qa_tab import QATab
from .settings_tab import SettingsTab


class MainWindow(QMainWindow):
    """Main application window with tabbed interface"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Meeting Notes - Professional Audio Transcription & AI Analysis")
        self.setGeometry(100, 100, 1400, 900)
        
        # Initialize UI components
        self.init_ui()
        self.init_menu_bar()
        self.init_toolbar()
        self.init_status_bar()
        
        # Apply modern styling
        self.apply_styling()
        
    def init_ui(self):
        """Initialize the main user interface"""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Create header section
        header_frame = self.create_header()
        main_layout.addWidget(header_frame)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.TabPosition.North)
        self.tab_widget.setMovable(True)
        self.tab_widget.setTabsClosable(False)
        
        # Initialize tabs
        self.audio_tab = AudioTab()
        self.transcription_tab = TranscriptionTab()
        self.summary_tab = SummaryTab()
        self.qa_tab = QATab()
        self.settings_tab = SettingsTab()
        
        # Add tabs to widget
        self.tab_widget.addTab(self.audio_tab, "🎵 Audio Files")
        self.tab_widget.addTab(self.transcription_tab, "📝 Transcription")
        self.tab_widget.addTab(self.summary_tab, "📋 AI Summary")
        self.tab_widget.addTab(self.qa_tab, "❓ Q&A Assistant")
        self.tab_widget.addTab(self.settings_tab, "⚙️ Settings")
        
        main_layout.addWidget(self.tab_widget)
        
        # Connect tab signals
        self.connect_tab_signals()
        
    def create_header(self):
        """Create application header with logo and title"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        header_frame.setMaximumHeight(80)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(15, 10, 15, 10)
        
        # Application title
        title_label = QLabel("Meeting Notes")
        title_font = QFont("Arial", 24, QFont.Weight.Bold)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #2c3e50; margin: 0px;")
        
        # Subtitle
        subtitle_label = QLabel("Professional Audio Transcription & AI-Powered Analysis")
        subtitle_font = QFont("Arial", 12)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setStyleSheet("color: #7f8c8d; margin-top: 5px;")
        
        # Title container
        title_container = QWidget()
        title_layout = QVBoxLayout(title_container)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(0)
        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)
        
        header_layout.addWidget(title_container)
        header_layout.addStretch()
        
        return header_frame
        
    def init_menu_bar(self):
        """Initialize the menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu('&File')
        
        # Open audio file action
        open_action = QAction('&Open Audio File...', self)
        open_action.setShortcut('Ctrl+O')
        open_action.setStatusTip('Open an audio or video file for transcription')
        open_action.triggered.connect(self.open_audio_file)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        # Export actions
        export_menu = file_menu.addMenu('&Export')
        
        export_word_action = QAction('Export to &Word...', self)
        export_word_action.setShortcut('Ctrl+W')
        export_word_action.triggered.connect(self.export_to_word)
        export_menu.addAction(export_word_action)
        
        export_pdf_action = QAction('Export to &PDF...', self)
        export_pdf_action.setShortcut('Ctrl+P')
        export_pdf_action.triggered.connect(self.export_to_pdf)
        export_menu.addAction(export_pdf_action)
        
        export_txt_action = QAction('Export to &Text...', self)
        export_txt_action.setShortcut('Ctrl+T')
        export_txt_action.triggered.connect(self.export_to_txt)
        export_menu.addAction(export_txt_action)
        
        file_menu.addSeparator()
        
        # Exit action
        exit_action = QAction('E&xit', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.setStatusTip('Exit the application')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Tools menu
        tools_menu = menubar.addMenu('&Tools')
        
        transcribe_action = QAction('&Start Transcription', self)
        transcribe_action.setShortcut('F5')
        transcribe_action.triggered.connect(self.start_transcription)
        tools_menu.addAction(transcribe_action)
        
        generate_summary_action = QAction('&Generate Summary', self)
        generate_summary_action.setShortcut('F6')
        generate_summary_action.triggered.connect(self.generate_summary)
        tools_menu.addAction(generate_summary_action)
        
        # Help menu
        help_menu = menubar.addMenu('&Help')
        
        about_action = QAction('&About', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def init_toolbar(self):
        """Initialize the toolbar"""
        toolbar = QToolBar()
        toolbar.setMovable(False)
        self.addToolBar(toolbar)
        
        # Open file button
        open_btn = QPushButton("📁 Open Audio")
        open_btn.clicked.connect(self.open_audio_file)
        toolbar.addWidget(open_btn)
        
        toolbar.addSeparator()
        
        # Transcribe button
        transcribe_btn = QPushButton("🎤 Transcribe")
        transcribe_btn.clicked.connect(self.start_transcription)
        toolbar.addWidget(transcribe_btn)
        
        # Summary button
        summary_btn = QPushButton("📋 Summarize")
        summary_btn.clicked.connect(self.generate_summary)
        toolbar.addWidget(summary_btn)
        
        toolbar.addSeparator()
        
        # Export buttons
        export_word_btn = QPushButton("📄 Word")
        export_word_btn.clicked.connect(self.export_to_word)
        toolbar.addWidget(export_word_btn)
        
        export_pdf_btn = QPushButton("📑 PDF")
        export_pdf_btn.clicked.connect(self.export_to_pdf)
        toolbar.addWidget(export_pdf_btn)
        
    def init_status_bar(self):
        """Initialize the status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Status label
        self.status_label = QLabel("Ready")
        self.status_bar.addWidget(self.status_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
    def connect_tab_signals(self):
        """Connect signals between tabs"""
        # Connect audio tab signals
        self.audio_tab.file_loaded.connect(self.on_audio_file_loaded)
        
        # Connect transcription tab signals
        self.transcription_tab.transcription_completed.connect(self.on_transcription_completed)
        
        # Connect summary tab signals
        self.summary_tab.summary_generated.connect(self.on_summary_generated)
        
    def apply_styling(self):
        """Apply modern styling to the application"""
        style = """
        QMainWindow {
            background-color: #f8f9fa;
        }
        
        QTabWidget::pane {
            border: 1px solid #dee2e6;
            background-color: white;
            border-radius: 5px;
        }
        
        QTabWidget::tab-bar {
            alignment: left;
        }
        
        QTabBar::tab {
            background-color: #e9ecef;
            border: 1px solid #dee2e6;
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
        }
        
        QTabBar::tab:selected {
            background-color: white;
            border-bottom-color: white;
        }
        
        QTabBar::tab:hover {
            background-color: #f8f9fa;
        }
        
        QPushButton {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #0056b3;
        }
        
        QPushButton:pressed {
            background-color: #004085;
        }
        
        QToolBar {
            background-color: #f8f9fa;
            border: none;
            spacing: 5px;
            padding: 5px;
        }
        
        QStatusBar {
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }
        """
        self.setStyleSheet(style)
        
    # Slot methods
    def open_audio_file(self):
        """Open audio file dialog"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Open Audio/Video File",
            "",
            "Audio/Video Files (*.mp3 *.wav *.m4a *.mp4 *.avi *.mov);;All Files (*)"
        )
        
        if file_path:
            self.audio_tab.load_file(file_path)
            self.status_label.setText(f"Loaded: {Path(file_path).name}")
            
    def start_transcription(self):
        """Start transcription process"""
        self.tab_widget.setCurrentWidget(self.transcription_tab)
        self.transcription_tab.start_transcription()
        
    def generate_summary(self):
        """Generate AI summary"""
        self.tab_widget.setCurrentWidget(self.summary_tab)
        self.summary_tab.generate_summary()
        
    def export_to_word(self):
        """Export to Word document"""
        # Implementation will be added in export module
        QMessageBox.information(self, "Export", "Word export functionality will be implemented")
        
    def export_to_pdf(self):
        """Export to PDF document"""
        # Implementation will be added in export module
        QMessageBox.information(self, "Export", "PDF export functionality will be implemented")
        
    def export_to_txt(self):
        """Export to text file"""
        # Implementation will be added in export module
        QMessageBox.information(self, "Export", "Text export functionality will be implemented")
        
    def show_about(self):
        """Show about dialog"""
        QMessageBox.about(
            self,
            "About Meeting Notes",
            "Meeting Notes v1.0.0\n\n"
            "Professional desktop application for audio transcription\n"
            "and AI-powered analysis.\n\n"
            "Features:\n"
            "• Audio/video file transcription\n"
            "• Speaker diarization\n"
            "• AI-powered summaries\n"
            "• Q&A assistant\n"
            "• Multi-format export\n\n"
            "Built with PyQt6 and powered by AI"
        )
        
    # Signal handlers
    def on_audio_file_loaded(self, file_path):
        """Handle audio file loaded signal"""
        self.status_label.setText(f"Audio loaded: {Path(file_path).name}")
        
    def on_transcription_completed(self, transcript):
        """Handle transcription completed signal"""
        self.status_label.setText("Transcription completed")
        # Enable summary tab
        self.summary_tab.set_transcript(transcript)
        self.qa_tab.set_transcript(transcript)
        
    def on_summary_generated(self, summary):
        """Handle summary generated signal"""
        self.status_label.setText("Summary generated")
        
    def show_progress(self, value, message="Processing..."):
        """Show progress in status bar"""
        self.progress_bar.setValue(value)
        self.progress_bar.setVisible(True)
        self.status_label.setText(message)
        
    def hide_progress(self):
        """Hide progress bar"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("Ready")
