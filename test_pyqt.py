#!/usr/bin/env python3
"""
Test PyQt6 imports and basic functionality
"""

import sys

try:
    from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QAction
    print("✅ All PyQt6 imports successful!")
    
    # Test basic application
    app = QApplication(sys.argv)
    window = QMainWindow()
    window.setWindowTitle("PyQt6 Test")
    window.setGeometry(100, 100, 400, 300)
    
    label = QLabel("PyQt6 is working!", window)
    label.setAlignment(Qt.AlignmentFlag.AlignCenter)
    window.setCentralWidget(label)
    
    print("✅ Basic PyQt6 application created successfully!")
    print("Close the window to continue...")
    
    window.show()
    sys.exit(app.exec())
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Checking what's available in PyQt6...")
    
    try:
        from PyQt6 import QtWidgets
        print(f"QtWidgets available: {dir(QtWidgets)[:10]}...")  # Show first 10 items
        
        from PyQt6 import QtGui
        print(f"QtGui available: {dir(QtGui)[:10]}...")  # Show first 10 items
        
        # Check if QAction is in QtGui
        if hasattr(QtGui, 'QAction'):
            print("✅ QAction found in QtGui")
        else:
            print("❌ QAction not found in QtGui")
            
        # Check if QAction is in QtWidgets
        if hasattr(QtWidgets, 'QAction'):
            print("✅ QAction found in QtWidgets")
        else:
            print("❌ QAction not found in QtWidgets")
            
    except ImportError as e2:
        print(f"❌ Even basic PyQt6 import failed: {e2}")
        
except Exception as e:
    print(f"❌ Other error: {e}")
