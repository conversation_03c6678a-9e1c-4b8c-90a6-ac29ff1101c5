"""
Configuration Manager for Meeting Notes Application
Handles application settings and configuration persistence
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional


class ConfigManager:
    """Manages application configuration and settings"""
    
    def __init__(self, config_dir: Optional[str] = None):
        """Initialize configuration manager
        
        Args:
            config_dir: Directory to store configuration files. 
                       If None, uses default user config directory.
        """
        if config_dir is None:
            # Use user's home directory for config
            self.config_dir = Path.home() / ".meeting_notes"
        else:
            self.config_dir = Path(config_dir)
            
        self.config_dir.mkdir(exist_ok=True)
        self.config_file = self.config_dir / "config.json"
        
        # Default configuration
        self.default_config = {
            # AI Providers
            "ai_providers": {
                "openai": {
                    "api_key": "",
                    "model": "gpt-3.5-turbo",
                    "organization": ""
                },
                "gemini": {
                    "api_key": "",
                    "model": "gemini-pro"
                },
                "groq": {
                    "api_key": "",
                    "model": "mixtral-8x7b-32768"
                },
                "local": {
                    "provider": "ollama",
                    "endpoint": "http://localhost:11434",
                    "model": "llama2"
                }
            },
            
            # Transcription Settings
            "transcription": {
                "whisper_model": "base",
                "device": "cpu",
                "language": "auto",
                "beam_size": 5,
                "temperature": 0.0,
                "enable_diarization": True,
                "min_speakers": 2,
                "max_speakers": 5,
                "normalize_audio": True,
                "noise_reduction": False,
                "chunk_size": 30
            },
            
            # Export Settings
            "export": {
                "default_format": "docx",
                "export_directory": str(Path.home() / "Documents" / "Meeting_Notes"),
                "word_settings": {
                    "include_header": True,
                    "include_toc": False,
                    "font": "Arial",
                    "font_size": 11
                },
                "pdf_settings": {
                    "page_numbers": True,
                    "bookmarks": False,
                    "page_size": "A4"
                }
            },
            
            # Application Settings
            "application": {
                "theme": "light",
                "language": "en",
                "auto_save": True,
                "show_notifications": True,
                "max_processes": 2,
                "memory_limit": 2048,
                "gpu_acceleration": False,
                "data_directory": str(self.config_dir / "data"),
                "retention_period": "forever"
            },
            
            # UI Settings
            "ui": {
                "window_geometry": {
                    "width": 1400,
                    "height": 900,
                    "x": 100,
                    "y": 100
                },
                "splitter_sizes": [600, 400],
                "last_tab": 0
            }
        }
        
        # Load existing configuration
        self.config = self.load_config()
        
    def load_config(self) -> Dict[str, Any]:
        """Load configuration from file"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    
                # Merge with defaults to ensure all keys exist
                return self._merge_configs(self.default_config, loaded_config)
                
            except (json.JSONDecodeError, IOError) as e:
                print(f"Error loading config: {e}. Using defaults.")
                return self.default_config.copy()
        else:
            return self.default_config.copy()
            
    def save_config(self) -> bool:
        """Save configuration to file
        
        Returns:
            True if successful, False otherwise
        """
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            return True
        except IOError as e:
            print(f"Error saving config: {e}")
            return False
            
    def get(self, key_path: str, default: Any = None) -> Any:
        """Get configuration value using dot notation
        
        Args:
            key_path: Dot-separated path to the configuration key
            default: Default value if key not found
            
        Returns:
            Configuration value or default
            
        Example:
            config.get("ai_providers.openai.api_key")
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
            
    def set(self, key_path: str, value: Any) -> None:
        """Set configuration value using dot notation
        
        Args:
            key_path: Dot-separated path to the configuration key
            value: Value to set
            
        Example:
            config.set("ai_providers.openai.api_key", "sk-...")
        """
        keys = key_path.split('.')
        config_ref = self.config
        
        # Navigate to the parent of the target key
        for key in keys[:-1]:
            if key not in config_ref:
                config_ref[key] = {}
            config_ref = config_ref[key]
            
        # Set the final key
        config_ref[keys[-1]] = value
        
    def get_ai_provider_config(self, provider: str) -> Dict[str, Any]:
        """Get configuration for specific AI provider
        
        Args:
            provider: Provider name (openai, gemini, groq, local)
            
        Returns:
            Provider configuration dictionary
        """
        return self.get(f"ai_providers.{provider}", {})
        
    def set_ai_provider_config(self, provider: str, config: Dict[str, Any]) -> None:
        """Set configuration for specific AI provider
        
        Args:
            provider: Provider name
            config: Provider configuration dictionary
        """
        self.set(f"ai_providers.{provider}", config)
        
    def get_transcription_config(self) -> Dict[str, Any]:
        """Get transcription configuration"""
        return self.get("transcription", {})
        
    def get_export_config(self) -> Dict[str, Any]:
        """Get export configuration"""
        return self.get("export", {})
        
    def get_application_config(self) -> Dict[str, Any]:
        """Get application configuration"""
        return self.get("application", {})
        
    def get_ui_config(self) -> Dict[str, Any]:
        """Get UI configuration"""
        return self.get("ui", {})
        
    def update_ui_config(self, ui_config: Dict[str, Any]) -> None:
        """Update UI configuration
        
        Args:
            ui_config: UI configuration dictionary
        """
        current_ui = self.get("ui", {})
        current_ui.update(ui_config)
        self.set("ui", current_ui)
        
    def reset_to_defaults(self) -> None:
        """Reset configuration to defaults"""
        self.config = self.default_config.copy()
        
    def _merge_configs(self, default: Dict[str, Any], loaded: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively merge loaded config with defaults
        
        Args:
            default: Default configuration
            loaded: Loaded configuration
            
        Returns:
            Merged configuration
        """
        result = default.copy()
        
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
                
        return result
        
    def create_data_directories(self) -> None:
        """Create necessary data directories"""
        data_dir = Path(self.get("application.data_directory"))
        data_dir.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories
        (data_dir / "transcripts").mkdir(exist_ok=True)
        (data_dir / "summaries").mkdir(exist_ok=True)
        (data_dir / "sessions").mkdir(exist_ok=True)
        (data_dir / "temp").mkdir(exist_ok=True)
        
        # Create export directory
        export_dir = Path(self.get("export.export_directory"))
        export_dir.mkdir(parents=True, exist_ok=True)
        
    def get_data_directory(self, subdir: str = "") -> Path:
        """Get data directory path
        
        Args:
            subdir: Subdirectory name (transcripts, summaries, sessions, temp)
            
        Returns:
            Path to data directory or subdirectory
        """
        data_dir = Path(self.get("application.data_directory"))
        if subdir:
            return data_dir / subdir
        return data_dir
        
    def get_export_directory(self) -> Path:
        """Get export directory path"""
        return Path(self.get("export.export_directory"))
        
    def validate_config(self) -> Dict[str, str]:
        """Validate configuration and return any errors
        
        Returns:
            Dictionary of validation errors (empty if valid)
        """
        errors = {}
        
        # Validate AI provider API keys
        providers = ["openai", "gemini", "groq"]
        for provider in providers:
            api_key = self.get(f"ai_providers.{provider}.api_key")
            if not api_key:
                errors[f"{provider}_api_key"] = f"{provider.title()} API key is not configured"
                
        # Validate directories
        data_dir = self.get("application.data_directory")
        if not data_dir:
            errors["data_directory"] = "Data directory is not configured"
        else:
            try:
                Path(data_dir).mkdir(parents=True, exist_ok=True)
            except Exception as e:
                errors["data_directory"] = f"Cannot create data directory: {e}"
                
        export_dir = self.get("export.export_directory")
        if not export_dir:
            errors["export_directory"] = "Export directory is not configured"
        else:
            try:
                Path(export_dir).mkdir(parents=True, exist_ok=True)
            except Exception as e:
                errors["export_directory"] = f"Cannot create export directory: {e}"
                
        return errors


# Global configuration instance
config = ConfigManager()
